/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.common.basic.livedata

import androidx.lifecycle.MutableLiveData

class EqualtableLiveData<T>(
    value: T? = null,
) : MutableLiveData<T>(value) {

    override fun setValue(value: T) {
        if (value == this.value) {
            return
        }
        super.setValue(value)
    }

    override fun postValue(value: T) {
        // this.value is a volatile value.
        if (value == this.value) {
            return
        }
        super.postValue(value)
    }
}
