/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.common.preference

import android.content.Context
import androidx.annotation.StringRes
import androidx.compose.ui.text.input.KeyboardType

class EditTextPreference(
    @StringRes override val titleId: Int,
    val summary: ((Context, String) -> String?)? = null,
    val content: String?,
    val placeholder: String? = null,
    val regex: Regex? = null,
    val regexError: String? = null,
    val keyboardType: KeyboardType? = null,
    val onValueChanged: (String) -> Unit,
) : Preference {

    companion object {
        val URL_REGEX = Regex(
            "^https://(www[.])?[-a-zA-Z0-9@:%._+~#=]{1,256}[.][a-zA-Z0-9()]{1,6}([-a-zA-Z0-9()!@:%_+.~#?&//=]*)/$"
        )
    }
}
