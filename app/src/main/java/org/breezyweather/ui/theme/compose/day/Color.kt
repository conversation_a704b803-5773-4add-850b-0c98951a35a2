/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.ui.theme.compose.day
import androidx.compose.ui.graphics.Color

val day_md_theme_light_primary = Color(0xFF205daf)
val day_md_theme_light_onPrimary = Color(0xFFffffff)
val day_md_theme_light_primaryContainer = Color(0xFFd6e3ff)
val day_md_theme_light_onPrimaryContainer = Color(0xFF001b3f)
val day_md_theme_light_secondary = Color(0xFF00677e)
val day_md_theme_light_onSecondary = Color(0xFFffffff)
val day_md_theme_light_secondaryContainer = Color(0xFFb1ebff)
val day_md_theme_light_onSecondaryContainer = Color(0xFF001f28)
val day_md_theme_light_tertiary = Color(0xFF775a00)
val day_md_theme_light_onTertiary = Color(0xFFffffff)
val day_md_theme_light_tertiaryContainer = Color(0xFFffdf8e)
val day_md_theme_light_onTertiaryContainer = Color(0xFF251a00)
val day_md_theme_light_error = Color(0xFFB3261E)
val day_md_theme_light_errorContainer = Color(0xFFF9DEDC)
val day_md_theme_light_onError = Color(0xFFFFFFFF)
val day_md_theme_light_onErrorContainer = Color(0xFF410E0B)
val day_md_theme_light_background = Color(0xFFfdfbff)
val day_md_theme_light_onBackground = Color(0xFF1b1b1d)
val day_md_theme_light_surface = Color(0xFFfdfbff)
val day_md_theme_light_onSurface = Color(0xFF1b1b1d)
val day_md_theme_light_surfaceVariant = Color(0xFFEFF4F8)
val day_md_theme_light_onSurfaceVariant = Color(0xFF49454F)
val day_md_theme_light_outline = Color(0x10000000)
val day_md_theme_light_inverseOnSurface = Color(0xFFf2f0f4)
val day_md_theme_light_inverseSurface = Color(0xFF2f3033)
val day_md_theme_light_inversePrimary = Color(0xFFa8c7ff)
val day_md_theme_light_shadow = Color(0xFF000000)
