/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.ui.theme.compose.night
import androidx.compose.ui.graphics.Color

val night_md_theme_dark_primary = Color(0xFFa8c7ff)
val night_md_theme_dark_onPrimary = Color(0xFF002f67)
val night_md_theme_dark_primaryContainer = Color(0xFF004591)
val night_md_theme_dark_onPrimaryContainer = Color(0xFFd6e3ff)
val night_md_theme_dark_secondary = Color(0xFF5bd5f9)
val night_md_theme_dark_onSecondary = Color(0xFF003542)
val night_md_theme_dark_secondaryContainer = Color(0xFF004e60)
val night_md_theme_dark_onSecondaryContainer = Color(0xFFb1ebff)
val night_md_theme_dark_tertiary = Color(0xFFeec148)
val night_md_theme_dark_onTertiary = Color(0xFF3f2e00)
val night_md_theme_dark_tertiaryContainer = Color(0xFF5a4300)
val night_md_theme_dark_onTertiaryContainer = Color(0xFFffdf8e)
val night_md_theme_dark_error = Color(0xFFF2B8B5)
val night_md_theme_dark_errorContainer = Color(0xFF8C1D18)
val night_md_theme_dark_onError = Color(0xFF601410)
val night_md_theme_dark_onErrorContainer = Color(0xFFF9DEDC)
val night_md_theme_dark_background = Color(0xFF1b1b1d)
val night_md_theme_dark_onBackground = Color(0xFFe3e2e6)
val night_md_theme_dark_surface = Color(0xFF1b1b1d)
val night_md_theme_dark_onSurface = Color(0xFFe3e2e6)
val night_md_theme_dark_surfaceVariant = Color(0xFF46464F)
val night_md_theme_dark_onSurfaceVariant = Color(0xFFCAC4D0)
val night_md_theme_dark_outline = Color(0x10ffffff)
val night_md_theme_dark_inverseOnSurface = Color(0xFF1b1b1d)
val night_md_theme_dark_inverseSurface = Color(0xFFe3e2e6)
val night_md_theme_dark_inversePrimary = Color(0xFF205daf)
val night_md_theme_dark_shadow = Color(0xFF000000)
