/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.ui.common.decorations

import android.content.Context
import android.graphics.Canvas
import android.graphics.Paint
import android.graphics.Rect
import android.view.View
import androidx.annotation.ColorInt
import androidx.annotation.Px
import androidx.recyclerview.widget.RecyclerView
import androidx.recyclerview.widget.RecyclerView.ItemDecoration
import org.breezyweather.common.extensions.dpToPx

/**
 * List decoration.
 */
class ListDecoration(
    context: Context,
    @ColorInt colorP: Int,
) : ItemDecoration() {
    private val mPaint: Paint

    @Px
    private val mDividerDistance: Int

    init {
        mDividerDistance = context.dpToPx(1f).toInt()
        mPaint = Paint().apply {
            color = colorP
            style = Paint.Style.STROKE
            strokeWidth = mDividerDistance.toFloat()
        }
    }

    override fun onDraw(c: Canvas, parent: RecyclerView, state: RecyclerView.State) {
        for (i in 0 until parent.childCount) {
            val child = parent.getChildAt(i)
            c.drawLine(
                child.left.toFloat(),
                child.bottom + mDividerDistance / 2f,
                child.right.toFloat(),
                child.bottom + mDividerDistance / 2f,
                mPaint
            )
        }
    }

    override fun getItemOffsets(outRect: Rect, view: View, parent: RecyclerView, state: RecyclerView.State) {
        outRect.set(0, 0, 0, mDividerDistance)
    }

    @get:ColorInt
    var color: Int
        get() = mPaint.color
        set(color) {
            mPaint.color = color
        }
}
