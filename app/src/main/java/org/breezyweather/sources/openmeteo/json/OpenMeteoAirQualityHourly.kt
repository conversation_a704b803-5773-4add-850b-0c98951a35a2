/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.sources.openmeteo.json

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable

@Serializable
data class OpenMeteoAirQualityHourly(
    val time: LongArray,
    val pm10: Array<Double?>?,
    @SerialName("pm2_5") val pm25: Array<Double?>?,
    @SerialName("carbon_monoxide") val carbonMonoxide: Array<Double?>?,
    @SerialName("nitrogen_dioxide") val nitrogenDioxide: Array<Double?>?,
    @SerialName("sulphur_dioxide") val sulphurDioxide: Array<Double?>?,
    val ozone: Array<Double?>?,
    @SerialName("alder_pollen") val alderPollen: Array<Double?>?,
    @SerialName("birch_pollen") val birchPollen: Array<Double?>?,
    @SerialName("grass_pollen") val grassPollen: Array<Double?>?,
    @SerialName("mugwort_pollen") val mugwortPollen: Array<Double?>?,
    @SerialName("olive_pollen") val olivePollen: Array<Double?>?,
    @SerialName("ragweed_pollen") val ragweedPollen: Array<Double?>?,
)
