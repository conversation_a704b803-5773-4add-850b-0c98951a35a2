/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.sources.climweb

import io.reactivex.rxjava3.core.Observable
import org.breezyweather.sources.climweb.json.ClimWebAlertsResult
import org.breezyweather.sources.climweb.json.ClimWebLocation
import org.breezyweather.sources.climweb.json.ClimWebNormals
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query

/**
 * ClimWeb API
 */
interface ClimWebApi {
    @GET("api/cities")
    fun getLocationList(
        @Query("format") format: String = "json",
    ): Observable<List<ClimWebLocation>>

    @GET("api/cap/alerts.geojson")
    fun getAlerts(): Observable<ClimWebAlertsResult>

    @GET("api/cityclimate/data/{page_id}/")
    fun getNormals(
        @Path("page_id") pageId: String,
        @Query("city_id") cityId: String,
    ): Observable<List<ClimWebNormals>>
}
