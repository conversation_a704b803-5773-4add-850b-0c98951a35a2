<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <!-- App -->
    <string name="app_name">Weather</string>
    <string name="breezy_weather" translatable="false">Breezy Weather</string>
    <!-- Card names -->
    <string name="forecast">Forecast</string>
    <string name="daily_forecast">Daily forecast</string>
    <string name="hourly_forecast">Hourly forecast</string>
    <string name="current_weather">Current weather</string>
    <!-- Nowcasting is a FORECAST of the next 2-3 hours, also called "immediate forecast" -->
    <string name="precipitation_nowcasting">Precipitation nowcasting</string>
    <string name="air_quality">Air quality</string>
    <!-- We use it singular in English as in “Grass pollen” -->
    <string name="pollen">Pollen</string>
    <string name="pollen_and_mold">Pollen &amp; mold</string>
    <string name="ephemeris">Sun &amp; moon</string>
    <string name="ephemeris_about">About rise and set times</string>
    <string name="ephemeris_about_rise">The rise time is when the sun or moon fully appears above the horizon.</string>
    <string name="ephemeris_about_set">The set time is when the sun or moon completely disappears below the horizon.</string>
    <string name="details">Details</string>
    <!-- Alerts -->
    <string name="alert">Alert</string>
    <string name="alerts">Alerts</string>
    <!-- Ex: 4 weather alerts -->
    <string name="alerts_to_follow">Upcoming alerts, tap here to see them.</string>
    <string name="alert_source">Source: %s</string>
    <!-- Daily/hourly forecast columns, must be as short as possible. -->
    <string name="daily_yesterday">Yesterday</string>
    <string name="daily_today">Today</string>
    <string name="daily_tomorrow">Tomorrow</string>
    <string name="daily_yesterday_short">Yda</string>
    <string name="daily_today_short">Today</string>
    <string name="daily_tomorrow_short">Tmw</string>
    <string name="daily_summary">Daily summary</string>
    <!-- Charts -->
    <string name="chart_no_daily_data">No data available for this day.</string>
    <string name="chart_no_hourly_data">No hourly data available.</string>
    <string name="chart_not_enough_hourly_data">Not enough hourly data available to display a chart for this day.</string>
    <string name="chart_average">Average</string>
    <string name="chart_maximum_value">Maximum value</string>
    <!-- Conditions -->
    <string name="conditions">Conditions</string>
    <!-- Temperature -->
    <string name="temperature">Temperature</string>
    <string name="temperature_real">Actual</string>
    <string name="temperature_real_details">The actual temperature.</string>
    <string name="temperature_feels_like">Feels like</string>
    <!-- 10 characters max version of temperature_feels_like to be used in current details header. May be less in cyrillic/asian alphabets due to large letters -->
    <string name="temperature_feels_like_short">Feels like</string>
    <string name="temperature_feels_like_details">What the temperature feels like, as a result of humidity, wind and/or sunlight.</string>
    <string name="temperature_real_feel">RealFeel®</string>
    <string name="temperature_real_feel_shade">RealFeel Shade™</string>
    <string name="temperature_apparent">Apparent</string>
    <string name="temperature_wind_chill">Wind chill</string>
    <string name="temperature_wet_bulb">Wet bulb</string>
    <string name="temperature_degree_day">Degree day</string>
    <string name="temperature_degree_day_heating">Heating degree day</string>
    <string name="temperature_degree_day_heating_explanation">Difference between the outside temperature and a reference comfortable temperature used to estimate heating energy requirements</string>
    <string name="temperature_degree_day_cooling">Cooling degree day</string>
    <string name="temperature_degree_day_cooling_explanation">Difference between the outside temperature and a reference comfortable temperature used to estimate cooling energy requirements</string>
    <!-- It’s actually a median, but the word “Average” is probably easier to understand -->
    <string name="temperature_average_short">Avg</string>
    <string name="temperature_normal_short">Normal</string>
    <string name="temperature_normals">Temperature normals</string>
    <!-- Precipitation -->
    <string name="precipitation">Precipitation</string>
    <string name="precipitation_probability">Precipitation probability</string>
    <string name="precipitation_duration">Precipitation duration</string>
    <string name="precipitation_total">Total</string>
    <string name="precipitation_thunderstorm">Thunderstorm</string>
    <string name="precipitation_rain">Rain</string>
    <string name="precipitation_snow">Snow</string>
    <string name="precipitation_ice">Ice</string>
    <string name="precipitation_intensity_light">Light</string>
    <string name="precipitation_intensity_medium">Medium</string>
    <string name="precipitation_intensity_heavy">Heavy</string>
    <string name="precipitation_none">No precipitation</string>
    <!-- Precipitation from 09:02 to 09:34 -->
    <string name="precipitation_between_time">Precipitation from %1$s to %2$s</string>
    <!-- Wind -->
    <string name="wind">Wind</string>
    <!-- 10 characters max version of wind to be used in current details header. May be less in cyrillic/asian alphabets due to large letters -->
    <string name="wind_short">Wind</string>
    <string name="wind_speed">Wind speed</string>
    <string name="wind_speed_about">About wind speed and gusts</string>
    <string name="wind_speed_about_description">Wind speed is measured by averaging wind speeds over a period of time. Gusts are sudden bursts of wind typically lasting under 20 seconds.</string>
    <string name="wind_gusts">Wind gusts</string>
    <string name="wind_gusts_short">Gusts</string>
    <string name="wind_direction">Wind direction</string>
    <string name="wind_direction_variable">Variable</string>
    <string name="wind_direction_N">North</string>
    <string name="wind_direction_NE">North East</string>
    <string name="wind_direction_E">East</string>
    <string name="wind_direction_SE">South East</string>
    <string name="wind_direction_S">South</string>
    <string name="wind_direction_SW">South West</string>
    <string name="wind_direction_W">West</string>
    <string name="wind_direction_NW">North West</string>
    <string name="wind_direction_N_short">N</string>
    <string name="wind_direction_NE_short">NE</string>
    <string name="wind_direction_E_short">E</string>
    <string name="wind_direction_SE_short">SE</string>
    <string name="wind_direction_S_short">S</string>
    <string name="wind_direction_SW_short">SW</string>
    <string name="wind_direction_W_short">W</string>
    <string name="wind_direction_NW_short">NW</string>
    <string name="wind_strength">Wind strength</string>
    <string name="wind_strength_scale_description" translatable="false">@string/label_description</string>
    <string name="wind_strength_scale">Beaufort scale</string>
    <string name="wind_strength_0">Calm</string>
    <string name="wind_strength_1">Light air</string>
    <string name="wind_strength_2">Light breeze</string>
    <string name="wind_strength_3">Gentle breeze</string>
    <string name="wind_strength_4">Moderate breeze</string>
    <string name="wind_strength_5">Fresh breeze</string>
    <string name="wind_strength_6">Strong breeze</string>
    <string name="wind_strength_7">Moderate gale</string>
    <string name="wind_strength_8">Gale</string>
    <string name="wind_strength_9">Strong gale</string>
    <string name="wind_strength_10">Storm</string>
    <string name="wind_strength_11">Violent storm</string>
    <string name="wind_strength_12">Hurricane-force</string>
    <!-- UV -->
    <string name="uv_index">UV index</string>
    <!-- 10 characters max version of uv_index to be used in current details header. May be less in cyrillic/asian alphabets due to large letters -->
    <string name="uv_index_short">UV index</string>
    <string name="uv_index_0_2">Low</string>
    <string name="uv_index_3_5">Moderate</string>
    <string name="uv_index_6_7">High</string>
    <string name="uv_index_8_10">Very high</string>
    <string name="uv_index_11">Extreme</string>
    <string name="uv_index_maximum_value" translatable="false">@string/chart_maximum_value</string>
    <string name="uv_index_about">About UV index</string>
    <string name="uv_index_about_description">The UV index measures the strength of the sun’s ultraviolet radiation. The higher it is, the greater and faster it can cause sunburn, skin cancer and eye damage.</string>
    <string name="uv_index_scale">UV index scale</string>
    <string name="uv_index_description" translatable="false">@string/label_description</string>
    <string name="uv_index_time_to_burn">Time to burn</string>
    <!-- %s = 1000 meters converted -->
    <string name="uv_index_time_to_burn_explanations">The exact time to burn depends on the altitude, the presence of reflective ground like water or snow, your skin type, and the use of sunscreen.</string>
    <string name="uv_alert_level">Alert</string>
    <!-- Air quality -->
    <string name="air_quality_index">Air quality index</string>
    <string name="air_quality_index_short">AQI</string>
    <string name="air_quality_index_about">About air quality index</string>
    <string name="air_quality_index_about_description_1">Air quality index (AQI) is a scale designed to help you understand how polluted the air is in a particular area, and the potential risks to your health.</string>
    <string name="air_quality_index_about_description_2">The scale used in this app is based on World Health Organization recommendations, rather than local regulations, to have meaningful categories, and make it easier to compare pollution levels between countries.</string>
    <string name="air_quality_index_scale">Air quality index scale</string>
    <string name="air_quality_index_description" translatable="false">@string/label_description</string>
    <string name="air_quality_index_harmless_exposure">Harmless exposure</string>
    <string name="air_quality_level_1">Excellent</string>
    <string name="air_quality_level_1_description">The air quality is ideal for most individuals; enjoy your normal outdoor activities.</string>
    <string name="air_quality_level_1_harmless_exposure">&gt; 1 year</string>
    <string name="air_quality_level_2">Fair</string>
    <string name="air_quality_level_2_description">The air quality is generally acceptable for most individuals. However, sensitive groups may experience minor to moderate symptoms from long-term exposure.</string>
    <string name="air_quality_level_2_harmless_exposure">&lt; 1 year</string>
    <string name="air_quality_level_3">Poor</string>
    <string name="air_quality_level_3_description">The air has reached a high level of pollution and is unhealthy for sensitive groups. Reduce time spent outside if you are feeling symptoms such as difficulty breathing or throat irritation.</string>
    <string name="air_quality_level_3_harmless_exposure">&lt; 1 day</string>
    <string name="air_quality_level_4">Unhealthy</string>
    <string name="air_quality_level_4_description">Health effects can be immediately felt by sensitive groups. Healthy individuals may experience difficulty breathing and throat irritation with prolonged exposure. Limit outdoor activity.</string>
    <string name="air_quality_level_4_harmless_exposure">&lt; 1 hour</string>
    <string name="air_quality_level_5">Very unhealthy</string>
    <string name="air_quality_level_5_description">Health effects will be immediately felt by sensitive groups and should avoid outdoor activity. Healthy individuals are likely to experience difficulty breathing and throat irritation; consider staying indoors and rescheduling outdoor activities.</string>
    <string name="air_quality_level_5_harmless_exposure">&lt; a few minutes</string>
    <string name="air_quality_level_6">Dangerous</string>
    <string name="air_quality_level_6_description">Any exposure to the air, even for a few minutes, can lead to serious health effects on everybody. Avoid outdoor activities.</string>
    <string name="air_quality_level_6_harmless_exposure">&lt; 1 minute</string>
    <string name="air_quality_average" translatable="false">@string/chart_average</string>
    <string name="air_quality_pollutant_primary">Primary pollutant</string>
    <string name="air_quality_pollutant_details">Pollutant details</string>
    <string name="air_quality_health_information">Health information</string>
    <string name="air_quality_pm25">PM2.5</string>
    <string name="air_quality_pm25_voice">PM two point five</string>
    <!-- %s = 2.5 µm -->
    <string name="air_quality_pm25_full">Fine particulate matter (&lt; %s)</string>
    <string name="air_quality_pm10">PM10</string>
    <string name="air_quality_pm10_voice">PM ten</string>
    <!-- %s = 10 µm -->
    <string name="air_quality_pm10_full">Inhalable particulate matter (&lt; %s)</string>
    <string name="air_quality_pm_sources">Main sources are combustion processes (e.g. power plants, indoor heating, car exhausts, wildfires), mechanical processes (e.g. construction, mineral dust, agriculture) and biological particles (e.g. pollen, mold, bacteria, viruses).</string>
    <string name="air_quality_so2">SO₂</string>
    <string name="air_quality_so2_voice">Sulfur dioxide</string>
    <!-- Please keep the 2 in SO₂ written this way -->
    <string name="air_quality_so2_full">Sulfur dioxide (SO₂)</string>
    <string name="air_quality_so2_sources">Main sources are burning processes of sulfur-containing fuel in industry, transportation and power plants.</string>
    <string name="air_quality_no2">NO₂</string>
    <string name="air_quality_no2_voice">Nitrogen dioxide</string>
    <!-- Please keep the 2 in NO₂ written this way -->
    <string name="air_quality_no2_full">Nitrogen dioxide (NO₂)</string>
    <string name="air_quality_no2_sources">Main sources are fuel burning processes, such as those used in industry and transportation.</string>
    <string name="air_quality_o3">O₃</string>
    <string name="air_quality_o3_voice">Ozone</string>
    <!-- Please keep the 3 in O₃ written this way -->
    <string name="air_quality_o3_full">Ground-level ozone (O₃)</string>
    <string name="air_quality_o3_sources">Ozone is created in a chemical reaction between atmospheric oxygen, nitrogen oxides, carbon monoxide and organic compounds, in the presence of sunlight.</string>
    <string name="air_quality_co">CO</string>
    <string name="air_quality_co_voice">Carbon monoxide</string>
    <string name="air_quality_co_full">Carbon monoxide (CO)</string>
    <string name="air_quality_co_sources">Typically originates from incomplete combustion of carbon fuels, such as that which occurs in car engines and power plants.</string>
    <!-- Deprecated, turning off translations before deleting them all -->
    <string name="air_quality_pm_info_title" translatable="false">Particulates (PM2.5 &amp; PM10)</string>
    <string name="air_quality_pm_explanations_introduction" translatable="false">Particulates or atmospheric particulate matter are microscopic particles of solid or liquid matter suspended in the air.</string>
    <string name="air_quality_pm_explanations_origin" translatable="false">Some particulates occur naturally, originating from volcanoes, dust storms, forest and grassland fires, living vegetation and sea spray. Human activities also generate significant amounts of particulates, such as traffic and transportation, industrial activities, power plants, construction sites, waste burning or fields.</string>
    <string name="air_quality_pm_explanations_consequences" translatable="false">Particulates are the most harmful form of air pollution due to their ability to penetrate deep into the lungs and brain from blood streams, causing health problems such as heart disease, lung disease, and premature death.</string>
    <string name="air_quality_o3_info_introduction" translatable="false">Ground-level ozone (O₃) is a major component of smog. Although the same molecule, ground-level ozone can be harmful to human health, unlike stratospheric ozone that protects the earth from excess UV radiation.</string>
    <string name="air_quality_o3_info_origin" translatable="false">It is formed from photochemical reactions with pollutants such as volatile organic compounds, carbon monoxide and nitrogen oxides (NOₓ) emitted from vehicles, and industry. Due to the photochemical nature, the highest levels of ozone are seen during periods of sunny weather.</string>
    <string name="air_quality_o3_info_consequences" translatable="false">Exposure to excessive ozone can cause problems breathing, trigger asthma, reduce lung function and lead to lung disease.</string>
    <string name="air_quality_no2_info_introduction" translatable="false">Nitrogen dioxide (NO₂) is a suffocating, toxic reddish-brown gas with a characteristic pungent, acrid odor.</string>
    <string name="air_quality_no2_info_origin" translatable="false">Ambient sources of NO₂ results from high temperature combustion of fuels in processes such as those used for heating, transportation, industry and power generation. Household sources of nitrogen oxides (NOₓ) include equipment that burn fuels such as furnaces, fireplaces and gas stoves and ovens.</string>
    <string name="air_quality_no2_info_consequences" translatable="false">Exposure to nitrogen dioxide can irritate airways and aggravate respiratory diseases. Additionally, NO₂ is an important ozone precursor, a pollutant closely linked to asthma and other respiratory conditions.</string>
    <string name="air_quality_so2_info_introduction" translatable="false">Sulfur dioxide (SO₂) is a colourless toxic gas responsible for the odor of burnt matches.</string>
    <string name="air_quality_so2_info_origin" translatable="false">It is released naturally by volcanic activity and predominantly derived from the combustion of fossil fuels for domestic heating, industries and power generation.</string>
    <string name="air_quality_so2_info_consequences" translatable="false">Exposure to SO₂ is associated with asthma hospital admissions and emergency room visits.</string>
    <string name="air_quality_co_info_introduction" translatable="false">Carbon monoxide (CO) is a poisonous, flammable gas that is colorless, odorless, tasteless, and non irritant.</string>
    <string name="air_quality_co_info_origin" translatable="false">It is produced by the incomplete combustion of carbonaceous fuels such as wood, petrol, coal, natural gas and kerosene in simple stoves, open fires, wick lamps, furnaces, fireplaces. The predominant source of carbon monoxide (CO) in ambient air is from motor vehicles.</string>
    <string name="air_quality_co_info_consequences" translatable="false">Carbon monoxide diffuses across the lung tissues and into the bloodstream, making it difficult for the body’s cells to bind to oxygen. This lack of oxygen damages tissues and cells. Exposure to carbon monoxide can cause difficulties breathing, exhaustion, dizziness, and other flu-like symptoms. Exposure to high levels of carbon monoxide can be deadly.</string>
    <!-- Pollen -->
    <string name="pollen_primary">Primary pollen</string>
    <string name="pollen_details">Pollen details</string>
    <string name="pollen_tree">Tree</string>
    <string name="pollen_mold">Mold</string>
    <!-- Latin name: Acer. Use latin (not English!) if no matching translation -->
    <string name="pollen_acer">Maple</string>
    <!-- Latin name: Alnus. Use latin (not English!) if no matching translation -->
    <string name="pollen_alnus">Alder</string>
    <!-- Latin name: Alternaria. Use latin (not English!) if no matching translation -->
    <string name="pollen_alternaria">Alternaria</string>
    <!-- Latin name: Ambrosia. Use latin (not English!) if no matching translation -->
    <string name="pollen_ambrosia">Ragweed</string>
    <!-- Latin name: Artemisia. Use latin (not English!) if no matching translation -->
    <string name="pollen_artemisia">Mugwort</string>
    <!-- Latin name: Atriplex. Use latin (not English!) if no matching translation -->
    <string name="pollen_atriplex">Saltbush</string>
    <!-- Latin name: Betula. Use latin (not English!) if no matching translation -->
    <string name="pollen_betula">Birch</string>
    <!-- Latin name: Carpinus. Use latin (not English!) if no matching translation -->
    <string name="pollen_carpinus">Hornbeam</string>
    <!-- Latin name: Castanea. Use latin (not English!) if no matching translation -->
    <string name="pollen_castanea">Chestnut tree</string>
    <!-- Latin name: Corylus. Use latin (not English!) if no matching translation -->
    <string name="pollen_corylus">Hazel</string>
    <!-- Latin name: Cupressaceae-Taxaceae. Use latin (not English!) if no matching translation -->
    <string name="pollen_cupressaceae_taxaceae">Cypress</string>
    <!-- Latin name: Fraxinus. Use latin (not English!) if no matching translation -->
    <string name="pollen_fraxinus">Ash</string>
    <!-- Latin name: Juniperus. Use latin (not English!) if no matching translation -->
    <string name="pollen_juniperus">Juniper</string>
    <!-- Latin name: Olea. Use latin (not English!) if no matching translation -->
    <string name="pollen_olea">Olive tree</string>
    <!-- Latin name: Picea. Use latin (not English!) if no matching translation -->
    <string name="pollen_picea">Spruce</string>
    <!-- Latin name: Pinus. Use latin (not English!) if no matching translation -->
    <string name="pollen_pinus">Pine</string>
    <!-- Latin name: Plantaginaceae. Use latin (not English!) if no matching translation -->
    <string name="pollen_plantaginaceae">Plantain</string>
    <!-- Latin name: Platanus. Use latin (not English!) if no matching translation -->
    <string name="pollen_platanus">Plane-tree</string>
    <!-- Latin name: Poaeceae. Use latin (not English!) if no matching translation -->
    <string name="pollen_poaeceae">Grass</string>
    <!-- Latin name: Populus. Use latin (not English!) if no matching translation -->
    <string name="pollen_populus">Poplar</string>
    <!-- Latin name: Quercus. Use latin (not English!) if no matching translation -->
    <string name="pollen_quercus">Oak</string>
    <!-- Latin name: Rumex. Use latin (not English!) if no matching translation -->
    <string name="pollen_rumex">Sorrel</string>
    <!-- Latin name: Salix. Use latin (not English!) if no matching translation -->
    <string name="pollen_salix">Willow</string>
    <!-- Latin name: Tilia. Use latin (not English!) if no matching translation -->
    <string name="pollen_tilia">Linden</string>
    <!-- Latin name: Ulmus. Use latin (not English!) if no matching translation -->
    <string name="pollen_ulmus">Elm</string>
    <!-- Latin name: Urticaceae. Use latin (not English!) if no matching translation -->
    <string name="pollen_urticaceae">Nettle/Pellitory</string>
    <!-- Pollen level means “Potential risk of allergy” -->
    <string name="pollen_level_0">None</string>
    <string name="pollen_level_1">Very low</string>
    <string name="pollen_level_2">Low</string>
    <string name="pollen_level_3">Moderate</string>
    <string name="pollen_level_4">High</string>
    <string name="pollen_level_5">Very high</string>
    <string name="pollen_tap_to_see_more" translatable="false">Tap to see more…</string>
    <!-- Ephemeris -->
    <string name="ephemeris_moon_phase_new_moon">New moon</string>
    <string name="ephemeris_moon_phase_waxing_crescent">Waxing crescent</string>
    <string name="ephemeris_moon_phase_first_quarter">First quarter</string>
    <string name="ephemeris_moon_phase_waxing_gibbous">Waxing gibbous</string>
    <string name="ephemeris_moon_phase_full_moon">Full moon</string>
    <string name="ephemeris_moon_phase_waning_gibbous">Waning gibbous</string>
    <string name="ephemeris_moon_phase_last_quarter">Last quarter</string>
    <string name="ephemeris_moon_phase_waning_crescent">Waning crescent</string>
    <!-- Sunrise at 05:54 -->
    <string name="ephemeris_sunrise_at">Sunrise at %s</string>
    <!-- Sunset at 17:54 -->
    <string name="ephemeris_sunset_at">Sunset at %s</string>
    <!-- Moonrise at 05:54 -->
    <string name="ephemeris_moonrise_at">Moonrise at %s</string>
    <!-- Moonset at 17:54 -->
    <string name="ephemeris_moonset_at">Moonset at %s</string>
    <!-- Other weather details vocabulary -->
    <string name="daytime">Daytime</string>
    <string name="nighttime">Nighttime</string>
    <string name="nighttime_details">Begins in the evening and lasts until dawn next day</string>
    <string name="sunshine">Sunshine</string>
    <string name="sunshine_duration">Sunshine duration</string>
    <string name="humidity">Humidity</string>
    <!-- 10 characters max version of humidity to be used in current details header. May be less in cyrillic/asian alphabets due to large letters -->
    <string name="humidity_short">Humidity</string>
    <string name="humidity_dew_point">Humidity / Dew point</string>
    <string name="humidity_about">About relative humidity</string>
    <string name="humidity_about_description">Relative humidity, more commonly known just as humidity, is the percentage of water vapour present in the air compared to the maximum amount that the air can hold at a given temperature.</string>
    <string name="dew_point">Dew point</string>
    <!-- 10 characters max version of dew_point to be used in current details header. May be less in cyrillic/asian alphabets due to large letters -->
    <string name="dew_point_short">Dew point</string>
    <string name="dew_point_about">About dew point</string>
    <!-- %s = 100 % -->
    <string name="dew_point_about_description">Dew point is what the temperature would need to be cooled to in order to achieve a relative humidity of %s.</string>
    <string name="pressure">Pressure</string>
    <string name="pressure_short">Pressure</string>
    <string name="pressure_average" translatable="false">@string/chart_average</string>
    <string name="pressure_rising">Rising</string>
    <string name="pressure_steady">Steady</string>
    <string name="pressure_falling">Falling</string>
    <string name="pressure_about">About pressure</string>
    <!-- If there is no acronym MSLP in your language, just skip the parenthesis in the translation -->
    <string name="pressure_about_description1">Atmospheric pressure is the downward pressure exerted by the earth’s atmosphere. This app uses mean sea level pressure (MSLP) to allow easier comparison of pressure from places with different altitudes.</string>
    <string name="pressure_about_description2">Significant, rapid changes in pressure are used to predict changes in the weather. For example, a drop in pressure can mean that rain or snow is on the way, and rising pressure can mean that weather will improve. Abnormal atmospheric pressure may cause headaches, joint pain, fatigue and other effects.</string>
    <string name="visibility">Visibility</string>
    <string name="visibility_short">Visibility</string>
    <string name="visibility_about">About visibility</string>
    <string name="visibility_about_description">Visibility tells you how far away you can clearly see objects like buildings and hills. Visibility can be affected by precipitation, fog, dust, smoke or haze.</string>
    <!-- 10 to 20 km, %1$s = 10, %2$s = 20 km -->
    <string name="visibility_from_to_number">%1$s to %2$s</string>
    <!-- Clear to Perfectly clear, %1$s = Clear, %2$s = Perfectly clear -->
    <string name="visibility_from_to_description">%1$s to %2$s</string>
    <string name="visibility_very_poor">Very poor</string>
    <string name="visibility_poor">Poor</string>
    <string name="visibility_moderate">Moderate</string>
    <string name="visibility_good">Good</string>
    <string name="visibility_clear">Clear</string>
    <string name="visibility_perfectly_clear">Perfectly clear</string>
    <string name="cloud_cover">Cloud cover</string>
    <!-- 10 characters max version of cloud_cover to be used in current details header. Don’t copy English! If you can fit “Cloud cover” fully, do it! -->
    <string name="cloud_cover_short">Cover</string>
    <string name="cloud_cover_about">About cloud cover</string>
    <string name="cloud_cover_about_description">Cloud cover refers to the percentage of the sky obscured by clouds on average. The cloud cover is correlated to the sunshine duration.</string>
    <string name="ceiling">Ceiling</string>
    <!-- 10 characters max version of ceiling to be used in current details header. May be less in cyrillic/asian alphabets due to large letters -->
    <string name="ceiling_short">Ceiling</string>
    <!-- Weather sources -->
    <string name="data_sources">Data sources</string>
    <!-- %s = Breezy Weather -->
    <string name="data_modified">This material has been modified from the original by %s mainly to compute or extrapolate missing data.</string>
    <!-- Deprecated -->
    <string name="weather_data_by" translatable="false">Data by %s</string>
    <string name="weather_forecast_data_by" translatable="false">Forecast data by %s</string>
    <string name="weather_current_data_by" translatable="false">Current weather data by %s</string>
    <string name="weather_air_quality_data_by" translatable="false">Air quality data by %s</string>
    <string name="weather_pollen_data_by" translatable="false">Pollen data by %s</string>
    <string name="weather_air_quality_and_pollen_data_by" translatable="false">Air quality and pollen data by %s</string>
    <string name="weather_minutely_data_by" translatable="false">Precipitation nowcasting data by %s</string>
    <string name="weather_alert_data_by" translatable="false">Alert data by %s</string>
    <string name="weather_normals_data_by" translatable="false">Temperature normals data by %s</string>
    <!-- Address is singular -->
    <string name="location_reverse_geocoding_by" translatable="false">Address lookup by %s</string>
    <string name="weather_message_data_refresh_failed">Data refresh failed</string>
    <string name="weather_message_unsupported_feature">Source doesn’t support one of requested features</string>
    <string name="weather_message_invalid_location">Invalid location, please delete it and re-add</string>
    <string name="weather_message_too_frequent_refreshes">Please wait a few seconds before retrying…</string>
    <string name="weather_api_limit_reached_title">API requests limit reached</string>
    <string name="weather_api_limit_reached_content">To workaround the limit, add your own API key.</string>
    <string name="weather_api_unauthorized_title">API access unauthorized</string>
    <string name="weather_api_unauthorized_content">On some weather data sources, you need to provide your own API key. If you already provided one, please double-check the API key you provided.</string>
    <string name="weather_api_key_required_missing_title">Required API key missing</string>
    <string name="weather_api_key_required_missing_content">To use this source, you need to provide an API key.</string>
    <string name="weather_source_continent_worldwide">Worldwide</string>
    <string name="weather_source_continent_africa">Africa</string>
    <string name="weather_source_continent_asia">Asia</string>
    <string name="weather_source_continent_europe">Europe</string>
    <string name="weather_source_continent_north_america">North America</string>
    <string name="weather_source_continent_oceania">Oceania</string>
    <string name="weather_source_continent_south_america">South America</string>
    <string name="settings_weather_source_portal">Portal</string>
    <string name="weather_source_accu_preference_portal_developer" translatable="false">Developer</string>
    <string name="weather_source_accu_preference_portal_enterprise" translatable="false">Enterprise</string>
    <string name="setting_weather_source_accu_days">Days</string>
    <string name="weather_source_accu_preference_days_1">1 day</string>
    <string name="weather_source_accu_preference_days_5">5 days</string>
    <string name="weather_source_accu_preference_days_10">10 days</string>
    <string name="weather_source_accu_preference_days_15">15 days</string>
    <string name="setting_weather_source_accu_hours">Hours</string>
    <string name="weather_source_accu_preference_hours_1">1 hour</string>
    <string name="weather_source_accu_preference_hours_12">12 hours</string>
    <string name="weather_source_accu_preference_hours_24">24 hours</string>
    <string name="weather_source_accu_preference_hours_72">72 hours</string>
    <string name="weather_source_accu_preference_hours_120">120 hours</string>
    <string name="weather_source_accu_preference_hours_240">240 hours</string>
    <!-- Location -->
    <string name="locations">Locations</string>
    <string name="location_current">Current location</string>
    <string name="location_custom_name">Custom name</string>
    <!-- Address is singular -->
    <string name="location_reverse_geocoding">Address lookup</string>
    <string name="location_search_weather_source">Weather source</string>
    <!-- Ex: Location results by Open-Meteo -->
    <string name="location_results_by">Location results by %s</string>
    <string name="location_message_added">Location added</string>
    <string name="location_message_updated">Location updated</string>
    <string name="location_message_already_exists">Location list already contains this location with this forecast source.</string>
    <string name="location_message_deleted">Location deleted</string>
    <string name="location_delete_location_dialog_title">Delete location?</string>
    <string name="location_delete_location_dialog_message">You’re about to delete “%s” from your list of saved locations.</string>
    <string name="location_delete_location_dialog_message_no_name">You’re about to delete this location from your list of saved locations.</string>
    <string name="location_message_list_cannot_be_empty">Location list cannot be empty</string>
    <!-- If using an RTL language, change “left” to “right” -->
    <string name="location_swipe_to_delete">You can swipe left to delete this item</string>
    <string name="location_message_search_failed">Location search failed</string>
    <string name="location_message_failed_to_locate">Failed to find current location</string>
    <string name="location_message_permission_missing">Access location permission missing</string>
    <string name="location_message_permission_background_missing">Access location in background permission missing</string>
    <string name="location_message_location_access_off">Location access is off</string>
    <string name="location_message_reverse_geocoding_failed">Weather source failed to find a matching location</string>
    <string name="location_dialog_failed_to_locate_action_select_source">Use another location service</string>
    <string name="location_dialog_failed_to_locate_action_add_manually">Add location manually and delete “%s”</string>
    <string name="location_search_placeholder">Search for a location…</string>
    <string name="location_search_source">Location search source</string>
    <string name="location_search_change_source">Change location search source</string>
    <!-- Example: AccuWeather can’t find djglidfjg -->
    <string name="location_search_no_results">%1$s can’t find %2$s</string>
    <string name="location_search_no_results_advice">Make sure your search is spelled correctly and not too specific. Alternatively, you can try another location search source which may produce different results.</string>
    <string name="location_current_not_found_yet">Current location not found yet…</string>
    <!-- Example: Last updated 3 hours ago. Don't translate “3 hours ago”. For example in Spanish, “3 hours ago” will be replaced by “hace 3 horas” so don’t translate as “Last updated hace %s”, or it will display as “Last updated hace hace 3 horas” -->
    <string name="location_last_updated_x">Last updated %s</string>
    <!-- Displayed in the location list when the location has at least 1 alert currently active -->
    <string name="location_has_active_alerts">Active alert</string>
    <!-- To be deprecated -->
    <string name="location_service_native">Native API</string>
    <!-- Actions -->
    <string name="action_location_list">Location list</string>
    <string name="action_search">Search</string>
    <string name="action_add_current_location">Add current location</string>
    <string name="action_add_new_location">Add a new location</string>
    <string name="action_add_debug_location" translatable="false">Add debug location</string>
    <string name="action_edit_location">Edit this location</string>
    <string name="action_open_in_other_app">Open in another app</string>
    <string name="action_open_in_other_app_description">Open this location in another app</string>
    <string name="action_settings">Settings</string>
    <string name="action_preview">Preview</string>
    <string name="action_about">About</string>
    <string name="action_dependencies" translatable="false">@string/about_dependencies</string>
    <string name="action_app_store">App Store</string>
    <string name="action_refresh">Refresh</string>
    <string name="action_restart">Restart</string>
    <string name="action_more">More</string>
    <string name="action_edit">Edit</string>
    <string name="action_delete">Delete</string>
    <string name="action_back">Back</string>
    <string name="action_next">Next</string>
    <string name="action_done">Done</string>
    <string name="action_save">Save</string>
    <string name="action_undo">Undo</string>
    <string name="action_set">Set</string>
    <string name="action_help">Help</string>
    <string name="action_learn_more">Learn more</string>
    <string name="action_continue">Continue</string>
    <string name="action_confirm">Confirm</string>
    <string name="action_close">Close</string>
    <string name="action_close_menu">Close menu</string>
    <string name="action_toggle_data_type_menu">Toggle the data type selection menu</string>
    <string name="action_share">Share</string>
    <string name="action_download">Download</string>
    <string name="action_show_errors">Tap to see details</string>
    <string name="action_show">Show</string>
    <string name="action_enable">Enable</string>
    <string name="action_allow">Allow</string>
    <string name="action_grant_permission">Tap to grant the permission.</string>
    <!-- Button shown in the weather selection dialog -->
    <string name="action_help_me_choose">Help me choose</string>
    <!-- Labels -->
    <string name="label_description">Description</string>
    <string name="label_expanded">Expanded</string>
    <string name="label_collapsed">Collapsed</string>
    <!-- Dialogs -->
    <string name="dialog_app_update_check_title">Stay up-to-date</string>
    <string name="dialog_app_update_check_content">Tap here to enable notifications about app updates.</string>
    <string name="dialog_permissions_notification_title">Stay informed</string>
    <string name="dialog_permissions_notification_content">Tap here to enable notifications about weather alerts, or decide later in Settings.</string>
    <string name="dialog_permissions_location_title">Access location information</string>
    <string name="dialog_permissions_location_content">To use this feature, you need to grant the app permission to access your current location. It will only be shared with the selected weather sources. If you changed your mind, you can cancel the process and add a fixed location manually.</string>
    <string name="dialog_permissions_location_background_title">Access location in background</string>
    <string name="dialog_permissions_location_background_content">To allow weather refresh in the background to be made for the most recent current location known by Android, you need to set location permission to “Allow all the time”. If you don’t, weather will be refreshed based on the latest retrieved coordinates in the foreground, which can be outdated.</string>
    <string name="dialog_refresh_error_details_title">Refresh errors</string>
    <string name="dialog_refresh_error_details_content">Tap on error messages with a question mark to get further help.</string>
    <string name="dialog_time_picker_select_time">Select time</string>
    <string name="dialog_time_picker_input_time">Input time</string>
    <string name="dialog_time_picker_toggle_touch_input_voice">Switch to Touch Input</string>
    <string name="dialog_time_picker_toggle_text_input_voice">Switch to Text Input</string>
    <!-- Miscellaneous messages -->
    <!-- Please keep it as short as possible as only a limited number of characters can be displayed. -->
    <string name="message_multiple_refresh_errors">Refresh completed with errors</string>
    <string name="message_invalid_incomplete_data">Invalid or incomplete data received from server</string>
    <string name="message_outdated_server_data">Outdated data received from server</string>
    <string name="message_network_unavailable">Network unavailable</string>
    <string name="message_server_timeout">Request timed out</string>
    <string name="message_server_insecure_title">Secure connection failed</string>
    <string name="message_server_unavailable_title">Server unavailable</string>
    <string name="message_server_unavailable_content">This is usually a temporary error. If the error persists, please report an issue on GitHub.</string>
    <string name="message_parsing_error_title">Failed to parse weather data</string>
    <string name="message_parsing_error_content">Make sure you updated to latest version of the app. Server may be temporarily unavailable, in that case, please try again later. If the problem persists, please dump a crash log from debug settings, and report an issue on GitHub.</string>
    <string name="message_source_not_installed_error_title">Source no longer available</string>
    <!-- Free as in “libre -->
    <string name="message_source_not_installed_error_content_tip_1">This can happen when you added your location in the Standard flavor of the app and then switched to the flavor with only libre network sources. Going back to the Standard flavor may solve this problem.</string>
    <string name="message_source_not_installed_error_content_tip_2">Either change the weather sources for this location, or delete and re-add this location.</string>
    <string name="message_tap_again_to_exit">Tap again to exit</string>
    <!-- Widgets -->
    <string name="widget_day">Daily</string>
    <string name="widget_week">Weekly</string>
    <string name="widget_day_week">Daily + Weekly</string>
    <string name="widget_clock_day_horizontal">Clock + Daily (Horizontal style)</string>
    <string name="widget_clock_day_details">Clock + Daily (Details)</string>
    <string name="widget_clock_day_vertical">Clock + Daily (Vertical style)</string>
    <string name="widget_clock_day_week">Clock + Daily + Weekly</string>
    <string name="widget_text">Text</string>
    <string name="widget_trend_daily">Daily trend</string>
    <string name="widget_trend_hourly">Hourly trend</string>
    <string name="widget_multi_city">Multi city</string>
    <string name="widget_material_you_forecast">Material You – Forecast</string>
    <string name="widget_material_you_current">Material You – Current</string>
    <string name="widget_refreshing">Refreshing</string>
    <string name="widget_ellipsis">…</string>
    <string name="widget_label_view_style">View style</string>
    <string name="widget_label_show_widget_card">Background color</string>
    <string name="widget_label_show_widget_card_alpha">Opacity</string>
    <string name="widget_label_hide_subtitle">Hide subtitle</string>
    <string name="widget_label_hide_header">Hide header</string>
    <string name="widget_label_subtitle_data">Subtitle data</string>
    <string name="widget_label_text_color">Text color</string>
    <string name="widget_label_text_size">Text size</string>
    <string name="widget_label_clock_font">Clock font</string>
    <string name="widget_label_hide_alternate_calendar">Hide alternate calendar</string>
    <!-- RtL languages must change “right” to “left”!! -->
    <string name="widget_label_align_end">Align right</string>
    <string name="widget_subtitle_data_time">Refresh time</string>
    <string name="widget_subtitle_data_aqi">Air quality</string>
    <string name="widget_subtitle_data_wind">Wind</string>
    <string name="widget_subtitle_data_feels_like">Feels like temperature</string>
    <string name="widget_subtitle_data_alternate_calendar" translatable="false">@string/settings_appearance_calendar_title</string>
    <string name="widget_subtitle_data_custom">Custom</string>
    <!-- keep the “$cw$” part as is -->
    <string name="widget_custom_subtitle_explanation">You can input anything, but please pay attention to the length of the text. If you want to quote weather data, please use $ to reference the corresponding keyword.\n\nFor example, “Current weather is: $cw$.” will be displayed as “Current weather is: Clear sky.”.</string>
    <string name="widget_custom_subtitle_keyword_cw_description">Current weather</string>
    <string name="widget_custom_subtitle_keyword_ct_description">Current temperature (°C/°F)</string>
    <string name="widget_custom_subtitle_keyword_ctd_description">Current temperature (°)</string>
    <string name="widget_custom_subtitle_keyword_at_description">Current feels like temperature (°C/°F)</string>
    <string name="widget_custom_subtitle_keyword_atd_description">Current feels like temperature (°)</string>
    <string name="widget_custom_subtitle_keyword_cwd_description">Current wind</string>
    <string name="widget_custom_subtitle_keyword_caqi_description">Current air quality index</string>
    <string name="widget_custom_subtitle_keyword_cuv_description">Current UV</string>
    <string name="widget_custom_subtitle_keyword_ch_description">Current humidity</string>
    <string name="widget_custom_subtitle_keyword_cps_description">Current pressure</string>
    <string name="widget_custom_subtitle_keyword_cv_description">Current visibility</string>
    <string name="widget_custom_subtitle_keyword_cdp_description">Current dew point</string>
    <string name="widget_custom_subtitle_keyword_al_description">Alerts</string>
    <string name="widget_custom_subtitle_keyword_als_description">Alerts (short)</string>
    <string name="widget_custom_subtitle_keyword_l_description">Location</string>
    <string name="widget_custom_subtitle_keyword_lat_description">Latitude</string>
    <string name="widget_custom_subtitle_keyword_lon_description">Longitude</string>
    <string name="widget_custom_subtitle_keyword_ut_description">Update time</string>
    <string name="widget_custom_subtitle_keyword_d_description">Date</string>
    <string name="widget_custom_subtitle_keyword_lc_description" translatable="false">@string/settings_appearance_calendar_title</string>
    <string name="widget_custom_subtitle_keyword_w_description">Week</string>
    <string name="widget_custom_subtitle_keyword_ws_description">Week (short)</string>
    <string name="widget_custom_subtitle_keyword_dd_description">Daily description</string>
    <string name="widget_custom_subtitle_keyword_hd_description">Hourly description</string>
    <string name="widget_custom_subtitle_keyword_enter_description">Newline</string>
    <string name="widget_custom_subtitle_keyword_xdw_description">Daytime weather of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xnw_description">Nighttime weather of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xdt_description">Daytime temperature of today, tomorrow, …, 4 days later (°C/°F)</string>
    <string name="widget_custom_subtitle_keyword_xnt_description">Nighttime temperature of today, tomorrow, …, 4 days later (°C/°F)</string>
    <string name="widget_custom_subtitle_keyword_xdtd_description">Daytime temperature of today, tomorrow, …, 4 days later (°)</string>
    <string name="widget_custom_subtitle_keyword_xntd_description">Nighttime temperature of today, tomorrow, …, 4 days later (°)</string>
    <string name="widget_custom_subtitle_keyword_xdp_description">Daytime precipitation of today, tomorrow, …, 4 days later (%)</string>
    <string name="widget_custom_subtitle_keyword_xnp_description">Nighttime precipitation of today, tomorrow, …, 4 days later (%)</string>
    <string name="widget_custom_subtitle_keyword_xdwd_description">Daytime wind of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xnwd_description">Nighttime wind of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xaqi_description">Air quality index of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xpis_description">Pollen index summary of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xsr_description">Sunrise of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xss_description">Sunset of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xmr_description">Moonrise of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xms_description">Moonset of today, tomorrow, …, 4 days later</string>
    <string name="widget_custom_subtitle_keyword_xmp_description">Moon phase of today, tomorrow, …, 4 days later</string>
    <string name="widget_style_rectangle">Rectangle</string>
    <string name="widget_style_symmetry">Symmetry</string>
    <string name="widget_style_tile">Tile</string>
    <string name="widget_style_mini">Mini</string>
    <string name="widget_style_nano">Nano</string>
    <string name="widget_style_pixel">Pixel</string>
    <string name="widget_style_vertical">Vertical</string>
    <string name="widget_style_oreo">Oreo</string>
    <string name="widget_style_temperature">Temperature</string>
    <string name="widget_week_style_5_days">5 days</string>
    <string name="widget_week_style_3_days">3 days</string>
    <string name="widget_clock_font_sans_serif_light">Sans Serif Light</string>
    <string name="widget_clock_font_sans_serif">Sans Serif</string>
    <string name="widget_clock_font_sans_serif_black">Sans Serif Black</string>
    <string name="widget_clock_font_analog">Analog</string>
    <string name="widget_live_wallpaper_weather_kind">Weather</string>
    <!-- Time as in “daytime” / “nighttime” -->
    <string name="widget_live_wallpaper_day_night_type">Time</string>
    <!-- Settings (new design) -->
    <string name="settings_section_general">General</string>
    <string name="settings_background_updates">Background updates</string>
    <string name="settings_background_updates_summary">Refresh rate, troubleshoot</string>
    <string name="settings_background_updates_section_general" translatable="false">@string/settings_section_general</string>
    <string name="settings_background_updates_refresh_title">Refresh rate</string>
    <string name="settings_background_updates_refresh_never">Never</string>
    <string name="settings_background_updates_refresh_never_warning1">By turning off background updates, you will no longer receive notifications of alerts or precipitation. Widgets won’t be refreshed until you open the app.</string>
    <string name="settings_background_updates_refresh_never_warning2">The background update process is lightweight: it only refreshes your first location at your select rate, and secondary locations only if you need them, once a day. It’s battery-efficient and should not exceed %d MB of data consumption a month.</string>
    <string name="settings_background_updates_refresh_never_warning3">Consider using a less frequent refresh rate rather than turning off background updates, otherwise tap “Continue” if you still want to proceed.</string>
    <string name="settings_background_updates_refresh_30min">30 minutes</string>
    <string name="settings_background_updates_refresh_1h">1 hour</string>
    <string name="settings_background_updates_refresh_1h_30min">1.5 hours</string>
    <string name="settings_background_updates_refresh_2h">2 hours</string>
    <string name="settings_background_updates_refresh_3h">3 hours</string>
    <string name="settings_background_updates_refresh_6h">6 hours</string>
    <string name="settings_background_updates_refresh_12h">12 hours</string>
    <string name="settings_background_updates_refresh_24h">24 hours</string>
    <string name="settings_background_updates_refresh_skip_when_battery_low">Skip when battery is low</string>
    <string name="settings_background_updates_section_troubleshoot">Troubleshoot</string>
    <string name="settings_background_updates_battery_optimization">Disable battery optimization</string>
    <string name="settings_background_updates_battery_optimization_summary">This is the first thing to try when you don’t receive notifications or widgets are not refreshing.</string>
    <string name="settings_background_updates_battery_optimization_activity_not_found">Couldn’t open device settings.</string>
    <string name="settings_background_updates_battery_optimization_disabled">Battery optimization is already disabled.</string>
    <string name="settings_background_updates_dont_kill_my_app_title">Manufacturer specific instructions</string>
    <!-- Keep "Don’t kill my app!" in English -->
    <string name="settings_background_updates_dont_kill_my_app_summary">Some manufacturers have additional app restrictions that kill background services. The Don’t kill my app! website has info on how to fix it.</string>
    <!-- Worker is a technical term, you can switch to “Process” if that makes more sense in your language -->
    <string name="settings_background_updates_worker_info_title">Worker info</string>
    <!-- Last run: 2024-02-11 -->
    <string name="settings_background_updates_worker_info_summary">Last run: %s</string>
    <string name="settings_background_updates_worker_info_enqueued" translatable="false">Enqueued</string>
    <string name="settings_background_updates_worker_info_finished" translatable="false">Finished</string>
    <string name="settings_background_updates_worker_info_running" translatable="false">Running</string>
    <!-- “Location” here means “Locating you”, check your Android settings to know the correct word -->
    <string name="settings_location">Location</string>
    <string name="settings_location_summary">Location services settings</string>
    <string name="settings_location_section_general" translatable="false">@string/settings_section_general</string>
    <string name="settings_location_service">Location service</string>
    <string name="settings_location_access_permission_already_granted">Permission already granted</string>
    <string name="settings_location_access_switch_title">Location access</string>
    <string name="settings_location_access_switch_summaryOn">Allowed. You can add your current location as a location.</string>
    <string name="settings_location_access_switch_summaryOff">Please allow if you want to be able to add your current location as a location.</string>
    <string name="settings_location_access_background_title">Location access in background</string>
    <string name="settings_location_access_background_summaryOn">Enabled. App will be able to refresh weather data for your current location in background.</string>
    <string name="settings_location_access_background_summaryOff">Please enable “Allow all the time” to have background refreshes for your current location.</string>
    <string name="settings_location_access_precise_title">Device’s GPS usage</string>
    <string name="settings_location_access_precise_summaryOn">Enabled. Device’s network provider will be used in priority and most of the time, but GPS can also be used as a fallback.</string>
    <string name="settings_location_access_precise_summaryOff">Enable “Use precise location” if you have trouble getting a location.</string>
    <string name="settings_global">Global settings</string>
    <!-- In other words: "Preferences of the location" -->
    <string name="settings_per_location">Location preferences</string>
    <string name="settings_per_location_summary">Additional customizations for this location</string>
    <string name="settings_per_location_theme">Theme</string>
    <string name="settings_weather_sources">Weather sources</string>
    <string name="settings_weather_sources_summary">Default weather source for new locations, custom API keys, other source settings</string>
    <string name="settings_weather_sources_section_general" translatable="false">@string/settings_section_general</string>
    <string name="settings_weather_sources_default_source">Default source for new locations</string>
    <string name="settings_weather_source_unavailable">%s (unavailable)</string>
    <string name="settings_weather_source_not_configured">%s (not configured)</string>
    <string name="settings_weather_source_none">None</string>
    <string name="settings_weather_source_freenet_disclaimer">More weather sources are available from other flavors of the app. Tap here to learn more.</string>
    <string name="settings_weather_source_current_position_disclaimer">More weather sources may become available for your location after the first refresh.</string>
    <string name="settings_weather_sources_per_location_summary">Customize weather data sources used for this location</string>
    <string name="settings_source_default_value">Default (unchanged)</string>
    <string name="settings_source_api_key">API key</string>
    <string name="settings_location_source_baidu_ip_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_accu_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_open_weather_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_pirate_weather_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_here_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_msn_azure_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_mf_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_atmo_aura_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_atmo_france_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_atmo_grand_est_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_atmo_hdf_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_atmo_sud_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_cwa_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_bmkg_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_met_office_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_weather_source_aemet_api_key" translatable="false">@string/settings_source_api_key</string>
    <!-- Synonym of instance: Server -->
    <string name="settings_source_instance">Instance</string>
    <string name="settings_source_instance_invalid">Must be an https URL ending with a /</string>
    <!-- Synonym of instance: Server -->
    <string name="settings_source_instance_geocoding">Geocoding instance</string>
    <string name="settings_weather_source_anam_bf_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_anamet_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_bright_sky_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_dccms_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_dmn_ne_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_dwr_gm_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_ethiomet_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_gmet_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_igebu_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_imngb_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_mali_meteo_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_meteo_benin_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_meteo_tchad_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_mettelsat_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_msd_zw_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_pirate_weather_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_recosante_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_recosante_instance_geocoding" translatable="false">@string/settings_source_instance_geocoding</string>
    <string name="settings_weather_source_rnsa_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_sma_sc_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_sma_su_instance" translatable="false">@string/settings_source_instance</string>
    <string name="settings_weather_source_ssms_instance" translatable="false">@string/settings_source_instance</string>
    <!-- Synonym of instance: Server -->
    <string name="settings_weather_source_open_meteo_instance_forecast">Forecast instance</string>
    <!-- Synonym of instance: Server -->
    <string name="settings_weather_source_open_meteo_instance_air_quality">Air quality instance</string>
    <string name="settings_weather_source_open_meteo_instance_geocoding" translatable="false">@string/settings_source_instance_geocoding</string>
    <string name="settings_weather_source_open_meteo_weather_models">Weather models</string>
    <string name="settings_weather_source_open_meteo_weather_models_best_match">Best match</string>
    <string name="settings_weather_source_open_meteo_weather_models_best_match_description">Combines the best weather models for this location</string>
    <string name="settings_weather_source_open_meteo_weather_models_seamless">Seamless</string>
    <string name="settings_weather_source_open_meteo_weather_models_seamless_description">Combines all weather models from this provider into a seamless prediction</string>
    <string name="settings_source_geonames_api_key" translatable="false">@string/settings_source_api_key</string>
    <string name="settings_appearance">Appearance</string>
    <string name="settings_appearance_summary">Language, dark mode, icon pack, units</string>
    <string name="settings_appearance_language_title">Language</string>
    <string name="settings_appearance_calendar_title">Alternate calendar</string>
    <string name="settings_appearance_dark_mode_title">Dark mode</string>
    <string name="settings_appearance_dark_mode_locations_title">Day / Night mode for locations</string>
    <string name="settings_appearance_icon_pack_title">Icon pack</string>
    <string name="settings_icon_packs_title">Icon packs</string>
    <string name="settings_icon_packs_check_details">Check details</string>
    <string name="settings_icon_packs_weather_adaptive_icon">Weather icon</string>
    <string name="settings_icon_packs_weather_icon_light">Light icon</string>
    <string name="settings_icon_packs_weather_icon_grey">Grey icon</string>
    <string name="settings_icon_packs_weather_icon_dark">Dark icon</string>
    <string name="settings_icon_packs_get_more">Get more</string>
    <string name="settings_icon_providers_app_store" translatable="false">@string/action_app_store</string>
    <string name="settings_units">Units</string>
    <string name="settings_units_summary">Temperature, precipitation, distance, speed, pressure</string>
    <string name="settings_units_temperature">Temperature unit</string>
    <string name="settings_units_precipitation">Precipitation unit</string>
    <string name="settings_units_distance">Distance unit</string>
    <string name="settings_units_speed">Speed unit</string>
    <string name="settings_units_pressure">Pressure unit</string>
    <string name="settings_main">Main screen</string>
    <string name="settings_main_summary">Cards order, items displayed, animations</string>
    <!-- Data is plural here -->
    <string name="settings_main_section_displayed_data">Displayed data</string>
    <string name="settings_main_cards_title">Cards</string>
    <string name="settings_main_daily_trends_title">Daily trends</string>
    <string name="settings_main_hourly_trends_title">Hourly trends</string>
    <string name="settings_main_header_details_title">Details in header</string>
    <string name="settings_main_section_options">Options</string>
    <string name="settings_main_threshold_lines_on_charts">Threshold lines on charts</string>
    <string name="settings_main_section_animations">Animations</string>
    <string name="settings_main_background_animation_title">Background animation</string>
    <string name="settings_main_background_animation_enabled">Enabled</string>
    <string name="settings_main_background_animation_disabled">Disabled</string>
    <string name="settings_main_gravity_sensor_switch">Gravity sensor</string>
    <string name="settings_main_cards_fade_in_switch">Cards fade in</string>
    <!-- Elements = temperature in header, AQI, sun &amp; moon arc, etc -->
    <string name="settings_main_cards_other_element_animations_switch">Other element animations</string>
    <string name="settings_notifications">Notifications</string>
    <string name="settings_notifications_summary">Notifications of weather alerts, precipitations or forecast for the first location</string>
    <string name="settings_notifications_app_updates_check">Notifications of app updates</string>
    <string name="settings_notifications_permission">Notification permission required</string>
    <!-- %s is the string with the key action_grant_permission -->
    <string name="settings_notifications_permission_summary">Needed to notify about alerts, forecasts and app updates. %s</string>
    <string name="settings_notifications_section_general" translatable="false">@string/settings_section_general</string>
    <!-- “important” means we won’t send notifications for minor severity alerts -->
    <string name="settings_notifications_alerts_title">Notifications of severe weather alerts</string>
    <string name="settings_notifications_precipitations_title">Notifications of precipitations</string>
    <string name="settings_notifications_section_forecast">Forecast</string>
    <string name="settings_notifications_forecast_today_title">Notification for the day’s forecast</string>
    <string name="settings_notifications_forecast_time_today_title">Notification time for the day’s forecast</string>
    <string name="settings_notifications_forecast_tomorrow_title">Notification for next-day forecast</string>
    <string name="settings_notifications_forecast_time_tomorrow_title">Notification time for next-day forecast</string>
    <string name="settings_widgets">Widgets &amp; Live wallpaper</string>
    <string name="settings_widgets_summary">Set wallpaper, configure widgets used, notification widget</string>
    <string name="settings_widgets_section_general" translatable="false">@string/settings_section_general</string>
    <string name="settings_widgets_live_wallpaper_title">Live wallpaper</string>
    <string name="settings_widgets_live_wallpaper_summary">Set a weather animation as wallpaper</string>
    <!-- %s = Breezy Weather -->
    <string name="settings_widgets_live_wallpaper_error">Couldn’t launch wallpaper chooser. You can still enable %s wallpaper by choosing “Live Wallpaper” in your home screen wallpaper picker.</string>
    <string name="settings_widgets_week_icon_mode_title">Icon to display in weekly views</string>
    <string name="settings_widgets_monochrome_icons_title">Monochrome icons</string>
    <string name="settings_widgets_section_widgets_in_use">Widgets in use</string>
    <string name="settings_widgets_configure_widget_summary">Configure this widget</string>
    <string name="settings_widgets_section_notification_widget">Notification widget</string>
    <!-- %s is the string with the key action_grant_permission -->
    <string name="settings_widgets_notification_permission_summary">Needed to show the notification widget. %s</string>
    <string name="settings_widgets_notification_widget_title">Notification widget</string>
    <string name="settings_widgets_notification_persistent_switch">Persistent</string>
    <string name="settings_widgets_notification_style_title">Style</string>
    <string name="settings_widgets_notification_temp_icon_switch">Temperature as status bar icon</string>
    <string name="settings_widgets_notification_feels_like_switch">Use feels like temperature</string>
    <string name="settings_widgets_broadcast_title">Data sharing</string>
    <!-- %s is for example Gadgetbridge, as in "Send data of type Gadgetbridge" -->
    <string name="settings_widgets_broadcast_send_data_title">Send %s data</string>
    <string name="settings_widgets_broadcast_send_data_summary_empty">No compatible packages found</string>
    <string name="about_privacy_policy">Privacy policy</string>
    <string name="settings_debug">Debug</string>
    <!-- %s is the string with the key action_grant_permission -->
    <string name="settings_debug_notification_permission">Needed to notify about crash logs and failed weather updates. %s</string>
    <string name="settings_debug_summary">Crash logs</string>
    <string name="settings_debug_dump_crash_logs_title">Share crash logs</string>
    <string name="settings_debug_dump_crash_logs_summary">Saves error logs to a file for sharing with the developers</string>
    <string name="settings_debug_dump_crash_logs_saved">Crash logs saved</string>
    <string name="settings_debug_dump_crash_logs_tap_to_open">Tap to open</string>
    <!-- Only shown in debug builds, so we don’t need translations -->
    <string name="settings_debug_force_weather_update" translatable="false">Force weather update</string>
    <string name="settings_debug_section_refresh_error" translatable="false">Trigger refresh errors</string>
    <string name="settings_changes_apply_after_restart">Changes will take effect after restart</string>
    <string name="settings_items_drag_to_sort">Tap to drag the list items to sort</string>
    <string name="settings_item_delete">Delete this item</string>
    <string name="settings_enabled">Enabled</string>
    <string name="settings_disabled">Disabled</string>
    <string name="settings_unavailable_no_background_updates">Unavailable, enable background updates first</string>
    <string name="settings_unavailable_no_animations">Unavailable, enable animations in Android settings first</string>
    <string name="settings_automatic">Automatic</string>
    <string name="settings_follow_system">Follow system</string>
    <string name="settings_none">None</string>
    <string name="settings_color_day_night">Day / Night</string>
    <!-- Used as "Follow app preference/setting", NOT as in "Follow us on Facebook". Keep it short or translate it “Automatic” if too complicated -->
    <string name="settings_color_app">Follow app</string>
    <string name="settings_color_day_always">Always day</string>
    <string name="settings_color_night_always">Always night</string>
    <string name="settings_color_light">Light</string>
    <string name="settings_color_grey">Grey</string>
    <string name="settings_color_dark">Dark</string>
    <string name="settings_invalid_syntax">Invalid syntax</string>
    <!-- Units -->
    <string name="unit_bf">Bf</string>
    <string name="unit_bf_voice">on Beaufort scale</string>
    <string name="unit_mps">m/s</string>
    <string name="unit_mps_voice">Meters per second</string>
    <string name="unit_kph">km/h</string>
    <string name="unit_kph_voice">Kilometers per hour</string>
    <string name="unit_kn">kn</string>
    <string name="unit_kn_voice">Knots</string>
    <string name="unit_mph">mph</string>
    <string name="unit_mph_voice">Miles per hour</string>
    <string name="unit_ftps">ft/s</string>
    <string name="unit_ftps_voice">Feet per second</string>
    <string name="unit_mm">mm</string>
    <string name="unit_mm_voice">Millimeters</string>
    <string name="unit_cm">cm</string>
    <string name="unit_cm_voice">Centimeters</string>
    <string name="unit_in">inch</string>
    <string name="unit_in_voice">Inches</string>
    <string name="unit_lpsqm">L/m²</string>
    <string name="unit_lpsqm_voice">Liters per square meter</string>
    <string name="unit_mmph">mm/h</string>
    <string name="unit_mmph_voice">Millimeters per hour</string>
    <string name="unit_cmph">cm/h</string>
    <string name="unit_cmph_voice">Centimeters per hour</string>
    <string name="unit_inph">inch/h</string>
    <string name="unit_inph_voice">Inches per hour</string>
    <string name="unit_lpsqmph">L/m²/h</string>
    <string name="unit_lpsqmph_voice">Liters per square meter per hour</string>
    <string name="unit_celsius">°C</string>
    <string name="unit_celsius_short">°</string>
    <string name="unit_fahrenheit">°F</string>
    <string name="unit_fahrenheit_short">°</string>
    <string name="unit_kelvin">K</string>
    <string name="unit_kelvin_short">K</string>
    <string name="unit_km">km</string>
    <string name="unit_km_voice">Kilometers</string>
    <string name="unit_m">m</string>
    <string name="unit_m_voice">Meters</string>
    <string name="unit_mi">mi</string>
    <string name="unit_mi_voice">Miles</string>
    <string name="unit_nmi">nmi</string>
    <string name="unit_nmi_voice">Nautical miles</string>
    <string name="unit_ft">ft</string>
    <string name="unit_ft_voice">Feet</string>
    <string name="unit_mb">mb</string>
    <string name="unit_mb_voice">Millibars</string>
    <string name="unit_kpa">kPa</string>
    <string name="unit_kpa_voice">Kilopascals</string>
    <string name="unit_hpa">hPa</string>
    <string name="unit_hpa_voice">Hectopascals</string>
    <string name="unit_atm">atm</string>
    <string name="unit_atm_voice">Atmospheres</string>
    <string name="unit_mmhg">mmHg</string>
    <string name="unit_mmhg_voice">Millimeters of mercury</string>
    <string name="unit_inhg">inHg</string>
    <string name="unit_inhg_voice">Inches of mercury</string>
    <string name="unit_kgfpsqcm">kgf/cm²</string>
    <string name="unit_kgfpsqcm_voice">Kilogram force per square centimeter</string>
    <string name="unit_mugpcum">μg/m³</string>
    <string name="unit_mugpcum_voice">Micrograms per cubic meter</string>
    <string name="unit_mgpcum">mg/m³</string>
    <string name="unit_mgpcum_voice">Milligrams per cubic meter</string>
    <string name="unit_h">h</string>
    <string name="unit_h_voice">hours</string>
    <string name="unit_ppcm">/m³</string>
    <string name="unit_ppcm_voice">Per cubic meter</string>
    <string name="unit_mum">µm</string>
    <string name="unit_mum_voice">Micrometers</string>
    <!-- Notifications -->
    <string name="notification_refreshed_at">Refreshed at</string>
    <string name="notification_channel_alerts">Alerts</string>
    <string name="notification_channel_forecast">Forecast</string>
    <string name="notification_channel_widget">Widget</string>
    <string name="notification_channel_background_services">Background services</string>
    <string name="notification_channel_crash_logs">Crash logs</string>
    <string name="notification_channel_app_updates">App updates</string>
    <string name="notification_running_in_background">Running in background to keep updating</string>
    <string name="notification_updating_weather_data">Updating weather data (%1$d/%2$d)</string>
    <string name="notification_update_error">%d update(s) failed</string>
    <string name="notification_precipitation_starting">Precipitation soon</string>
    <!-- Example: Expected to start at 09:00 AM -->
    <string name="notification_precipitation_starting_desc">Expected to start at %s</string>
    <string name="notification_precipitation_continuing">Precipitation</string>
    <!-- Example: Expected to continue until at least 09:00 AM -->
    <string name="notification_precipitation_continuing_desc">Expected to continue until at least %s</string>
    <string name="notification_precipitation_stopping">Precipitation stopping soon</string>
    <!-- Example: Expected to stop at 09:00 AM -->
    <string name="notification_precipitation_stopping_desc">Expected to stop at %s</string>
    <string name="notification_style_native">Native</string>
    <string name="notification_style_cities">Cities</string>
    <string name="notification_app_update_available">New version available!</string>
    <!-- About -->
    <string name="about_app">About app</string>
    <string name="about_check_for_app_updates">Check for app updates</string>
    <string name="about_no_new_updates">No new updates available</string>
    <string name="about_contact">Contact</string>
    <string name="about_matrix">Matrix room</string>
    <string name="about_source_code">Source code</string>
    <string name="about_dependencies">Dependencies</string>
    <string name="about_contributors">Contributors</string>
    <string name="about_translators">Translators</string>
    <string name="about_contribution_WangDaYeeeeee">Developer of the original project Geometric Weather</string>
    <string name="about_contribution_designer">Logo designer</string>
    <!-- %s is a link -->
    <string name="about_open_link_message">Open %s?</string>
    <!-- Weather condition text -->
    <!-- Check https://unterm.un.org/unterm2/en/ -->
    <!-- We don’t use all of them currently, but they might come useful some day -->
    <string name="common_weather_text_clear_sky">Clear sky</string>
    <string name="common_weather_text_mainly_clear">Mainly clear</string>
    <string name="common_weather_text_partly_cloudy">Partly cloudy</string>
    <string name="common_weather_text_cloudy">Cloudy</string>
    <string name="common_weather_text_overcast">Overcast</string>
    <string name="common_weather_text_fog">Fog</string>
    <string name="common_weather_text_rain">Rain</string>
    <string name="common_weather_text_rain_light">Light rain</string>
    <string name="common_weather_text_rain_moderate">Moderate rain</string>
    <string name="common_weather_text_rain_heavy">Heavy rain</string>
    <!-- Rain showers is an intense rain, generally over a short period -->
    <string name="common_weather_text_rain_showers">Rain showers</string>
    <string name="common_weather_text_rain_showers_light">Light rain showers</string>
    <string name="common_weather_text_rain_showers_moderate">Moderate rain showers</string>
    <string name="common_weather_text_rain_showers_heavy">Heavy rain showers</string>
    <string name="common_weather_text_rain_freezing">Freezing rain</string>
    <string name="common_weather_text_rain_freezing_light">Light freezing rain</string>
    <string name="common_weather_text_rain_freezing_heavy">Heavy freezing rain</string>
    <!-- Don’t use “sleet” here as it doesn’t mean the same thing in en-US -->
    <!-- You can use “melting snow” or “wet snow” if sounds better in your language -->
    <string name="common_weather_text_rain_snow_mixed">Rain and snow mixed</string>
    <string name="common_weather_text_rain_snow_mixed_light">Light rain and snow mixed</string>
    <string name="common_weather_text_rain_snow_mixed_heavy">Heavy rain and snow mixed</string>
    <string name="common_weather_text_rain_snow_mixed_showers">Rain and snow mixed showers</string>
    <string name="common_weather_text_rain_snow_mixed_showers_light">Light rain and snow mixed showers</string>
    <string name="common_weather_text_rain_snow_mixed_showers_heavy">Heavy rain and snow mixed showers</string>
    <string name="common_weather_text_snow">Snow</string>
    <string name="common_weather_text_snow_light">Light snow</string>
    <string name="common_weather_text_snow_moderate">Moderate snow</string>
    <string name="common_weather_text_snow_heavy">Heavy snow</string>
    <string name="common_weather_text_snow_showers">Snow showers</string>
    <string name="common_weather_text_snow_showers_light">Light snow showers</string>
    <string name="common_weather_text_snow_showers_heavy">Heavy snow showers</string>
    <string name="common_weather_text_snow_grains">Snow grains</string>
    <!-- Drizzle is a very thin rain, almost can’t feel it -->
    <string name="common_weather_text_drizzle">Drizzle</string>
    <string name="common_weather_text_drizzle_light">Light drizzle</string>
    <string name="common_weather_text_drizzle_moderate">Moderate drizzle</string>
    <string name="common_weather_text_drizzle_heavy">Heavy drizzle</string>
    <string name="common_weather_text_drizzle_freezing">Freezing drizzle</string>
    <string name="common_weather_text_drizzle_freezing_light">Light freezing drizzle</string>
    <string name="common_weather_text_drizzle_freezing_heavy">Heavy freezing drizzle</string>
    <string name="common_weather_text_sand_storm">Sand storm</string>
    <string name="common_weather_text_mist">Mist</string>
    <string name="common_weather_text_dry">Dry</string>
    <string name="common_weather_text_humid">Humid</string>
    <string name="common_weather_text_hot">Hot</string>
    <string name="common_weather_text_warm">Warm</string>
    <string name="common_weather_text_cool">Cool</string>
    <string name="common_weather_text_cold">Cold</string>
    <string name="common_weather_text_smoke">Smoke</string>
    <string name="common_weather_text_dust">Dust</string>
    <string name="common_weather_text_dust_storm">Dust storm</string>
    <string name="common_weather_text_frost">Frost</string>
    <string name="common_weather_text_squall">Squall</string>
    <string name="common_weather_text_tornado">Tornado</string>
    <string name="common_weather_text_blowing_snow">Blowing snow</string>
    <string name="common_weather_text_drifting_snow">Drifting snow</string>
    <string name="common_weather_text_glaze">Glaze</string>
    <!-- Weather kind -->
    <string name="weather_kind_clear" translatable="false">@string/common_weather_text_clear_sky</string>
    <string name="weather_kind_partly_cloudy" translatable="false">@string/common_weather_text_partly_cloudy</string>
    <string name="weather_kind_cloudy" translatable="false">@string/common_weather_text_cloudy</string>
    <string name="weather_kind_rain" translatable="false">@string/common_weather_text_rain</string>
    <string name="weather_kind_snow" translatable="false">@string/common_weather_text_snow</string>
    <string name="weather_kind_sleet" translatable="false">@string/common_weather_text_rain_snow_mixed</string>
    <string name="weather_kind_hail">Hail</string>
    <string name="weather_kind_fog" translatable="false">@string/common_weather_text_fog</string>
    <string name="weather_kind_haze">Haze</string>
    <string name="weather_kind_thunder">Thunder</string>
    <string name="weather_kind_thunderstorm">Thunderstorm</string>
    <string name="weather_kind_wind">Wind</string>
    <!-- Weather texts for Open-Meteo, based on WMO terms -->
    <!-- https://www.nodc.noaa.gov/archive/arc0021/0002199/1.1/data/0-data/HTML/WMO-CODE/WMO4677.HTM -->
    <string name="openmeteo_weather_text_clear_sky" translatable="false">@string/common_weather_text_clear_sky</string>
    <string name="openmeteo_weather_text_mainly_clear" translatable="false">@string/common_weather_text_mainly_clear</string>
    <string name="openmeteo_weather_text_partly_cloudy" translatable="false">@string/common_weather_text_partly_cloudy</string>
    <string name="openmeteo_weather_text_overcast" translatable="false">@string/common_weather_text_overcast</string>
    <string name="openmeteo_weather_text_fog" translatable="false">@string/common_weather_text_fog</string>
    <string name="openmeteo_weather_text_depositing_rime_fog">Depositing rime fog</string>
    <string name="openmeteo_weather_text_drizzle_light_intensity" translatable="false">@string/common_weather_text_drizzle_light</string>
    <string name="openmeteo_weather_text_drizzle_moderate_intensity" translatable="false">@string/common_weather_text_drizzle_moderate</string>
    <string name="openmeteo_weather_text_drizzle_dense_intensity" translatable="false">@string/common_weather_text_drizzle_heavy</string>
    <string name="openmeteo_weather_text_freezing_drizzle_light_intensity" translatable="false">@string/common_weather_text_drizzle_freezing_light</string>
    <string name="openmeteo_weather_text_freezing_drizzle_dense_intensity" translatable="false">@string/common_weather_text_drizzle_freezing_heavy</string>
    <string name="openmeteo_weather_text_rain_slight_intensity" translatable="false">@string/common_weather_text_rain_light</string>
    <string name="openmeteo_weather_text_rain_moderate_intensity" translatable="false">@string/common_weather_text_rain_moderate</string>
    <string name="openmeteo_weather_text_rain_heavy_intensity" translatable="false">@string/common_weather_text_rain_heavy</string>
    <string name="openmeteo_weather_text_freezing_rain_light_intensity" translatable="false">@string/common_weather_text_rain_freezing_light</string>
    <string name="openmeteo_weather_text_freezing_rain_heavy_intensity" translatable="false">@string/common_weather_text_rain_freezing_heavy</string>
    <string name="openmeteo_weather_text_snow_slight_intensity" translatable="false">@string/common_weather_text_snow_light</string>
    <string name="openmeteo_weather_text_snow_moderate_intensity" translatable="false">@string/common_weather_text_snow_moderate</string>
    <string name="openmeteo_weather_text_snow_heavy_intensity" translatable="false">@string/common_weather_text_snow_heavy</string>
    <string name="openmeteo_weather_text_snow_grains" translatable="false">@string/common_weather_text_snow_grains</string>
    <string name="openmeteo_weather_text_rain_showers_slight" translatable="false">@string/common_weather_text_rain_showers_light</string>
    <string name="openmeteo_weather_text_rain_showers_moderate" translatable="false">@string/common_weather_text_rain_showers_moderate</string>
    <string name="openmeteo_weather_text_rain_showers_violent" translatable="false">@string/common_weather_text_rain_showers_heavy</string>
    <string name="openmeteo_weather_text_snow_showers_slight" translatable="false">@string/common_weather_text_snow_showers_light</string>
    <string name="openmeteo_weather_text_snow_showers_heavy" translatable="false">@string/common_weather_text_snow_showers_heavy</string>
    <string name="openmeteo_weather_text_thunderstorm_slight_or_moderate">Thunderstorm slight or moderate</string>
    <string name="openmeteo_weather_text_thunderstorm_with_slight_hail">Thunderstorm with slight hail</string>
    <string name="openmeteo_weather_text_thunderstorm_with_heavy_hail">Thunderstorm with heavy hail</string>
    <!-- Weather texts for MET Norway -->
    <string name="metno_weather_text_clearsky" translatable="false">@string/common_weather_text_clear_sky</string>
    <string name="metno_weather_text_cloudy" translatable="false">@string/common_weather_text_cloudy</string>
    <string name="metno_weather_text_fair" translatable="false">@string/common_weather_text_mainly_clear</string>
    <string name="metno_weather_text_fog" translatable="false">@string/common_weather_text_fog</string>
    <string name="metno_weather_text_heavyrain" translatable="false">@string/common_weather_text_rain_heavy</string>
    <string name="metno_weather_text_heavyrainshowers" translatable="false">@string/common_weather_text_rain_showers_heavy</string>
    <string name="metno_weather_text_heavysleet" translatable="false">@string/common_weather_text_rain_snow_mixed_heavy</string>
    <string name="metno_weather_text_heavysleetshowers" translatable="false">@string/common_weather_text_rain_snow_mixed_showers_heavy</string>
    <string name="metno_weather_text_heavysnow" translatable="false">@string/common_weather_text_snow_heavy</string>
    <string name="metno_weather_text_heavysnowshowers" translatable="false">@string/common_weather_text_snow_showers_heavy</string>
    <string name="metno_weather_text_lightrain" translatable="false">@string/common_weather_text_rain_light</string>
    <string name="metno_weather_text_lightrainshowers" translatable="false">@string/common_weather_text_rain_showers_light</string>
    <string name="metno_weather_text_lightsleet" translatable="false">@string/common_weather_text_rain_snow_mixed_light</string>
    <string name="metno_weather_text_lightsleetshowers" translatable="false">@string/common_weather_text_rain_snow_mixed_showers_heavy</string>
    <string name="metno_weather_text_lightsnow" translatable="false">@string/common_weather_text_snow_light</string>
    <string name="metno_weather_text_lightsnowshowers" translatable="false">@string/common_weather_text_snow_showers_light</string>
    <string name="metno_weather_text_partlycloudy" translatable="false">@string/common_weather_text_partly_cloudy</string>
    <string name="metno_weather_text_rain" translatable="false">@string/common_weather_text_rain</string>
    <string name="metno_weather_text_rainshowers" translatable="false">@string/common_weather_text_rain_showers</string>
    <string name="metno_weather_text_sleet" translatable="false">@string/common_weather_text_rain_snow_mixed</string>
    <string name="metno_weather_text_sleetshowers" translatable="false">@string/common_weather_text_rain_snow_mixed_showers</string>
    <string name="metno_weather_text_snow" translatable="false">@string/common_weather_text_snow</string>
    <string name="metno_weather_text_snowshowers" translatable="false">@string/common_weather_text_snow_showers</string>
    <!-- Weather texts for Meteo AM -->
    <string name="meteoam_weather_text_clear_sky" translatable="false">@string/common_weather_text_clear_sky</string>
    <string name="meteoam_weather_text_mainly_cloudy" translatable="false">@string/common_weather_text_mainly_clear</string>
    <string name="meteoam_weather_text_partly_cloudy" translatable="false">@string/common_weather_text_partly_cloudy</string>
    <string name="meteoam_weather_text_cloudy" translatable="false">@string/common_weather_text_cloudy</string>
    <string name="meteoam_weather_text_overcast" translatable="false">@string/common_weather_text_overcast</string>
    <string name="meteoam_weather_text_rain_light" translatable="false">@string/common_weather_text_rain_light</string>
    <string name="meteoam_weather_text_rain_heavy" translatable="false">@string/common_weather_text_rain_heavy</string>
    <string name="meteoam_weather_text_rain_snow_mixed" translatable="false">@string/common_weather_text_rain_snow_mixed</string>
    <string name="meteoam_weather_text_rain_freezing" translatable="false">@string/common_weather_text_rain_freezing</string>
    <string name="meteoam_weather_text_fog" translatable="false">@string/common_weather_text_fog</string>
    <string name="meteoam_weather_text_snow" translatable="false">@string/common_weather_text_snow</string>
    <string name="meteoam_weather_text_thunderstorm" translatable="false">@string/weather_kind_thunderstorm</string>
    <string name="meteoam_weather_text_haze" translatable="false">@string/weather_kind_haze</string>
    <string name="meteoam_weather_text_hail" translatable="false">@string/weather_kind_hail</string>
    <!-- Literally "air or marine tornado" in Italian -->
    <string name="meteoam_weather_text_tornado_watersprout">Tornado or waterspout</string>
    <string name="meteoam_weather_text_smoke" translatable="false">@string/common_weather_text_smoke</string>
    <string name="meteoam_weather_text_sand_storm" translatable="false">@string/common_weather_text_sand_storm</string>
    <!-- Example: Heavy rain and thunder -->
    <string name="metno_weather_text_andthunder">%s and thunder</string>
    <!-- Weather texts for HKO -->
    <string name="hko_weather_text_sunny" translatable="false">@string/common_weather_text_clear_sky</string>
    <string name="hko_weather_text_sunny_periods" translatable="false">Sunny periods</string>
    <string name="hko_weather_text_sunny_intervals" translatable="false">Sunny intervals</string>
    <string name="hko_weather_text_sunny_periods_with_a_few_showers" translatable="false">Sunny periods with a few showers</string>
    <string name="hko_weather_text_sunny_intervals_with_showers" translatable="false">Sunny intervals with showers</string>
    <string name="hko_weather_text_cloudy" translatable="false">@string/common_weather_text_cloudy</string>
    <string name="hko_weather_text_overcast" translatable="false">@string/common_weather_text_overcast</string>
    <string name="hko_weather_text_light_rain" translatable="false">@string/common_weather_text_rain_light</string>
    <string name="hko_weather_text_rain" translatable="false">@string/common_weather_text_rain</string>
    <string name="hko_weather_text_heavy_rain" translatable="false">@string/common_weather_text_rain_heavy</string>
    <string name="hko_weather_text_thunderstorms" translatable="false">@string/weather_kind_thunderstorm</string>
    <string name="hko_weather_text_fine" translatable="false">@string/common_weather_text_clear_sky</string>
    <string name="hko_weather_text_mainly_cloudy" translatable="false">@string/common_weather_text_cloudy</string>
    <string name="hko_weather_text_mainly_fine" translatable="false">@string/common_weather_text_mainly_clear</string>
    <string name="hko_weather_text_windy" translatable="false">@string/weather_kind_wind</string>
    <string name="hko_weather_text_dry" translatable="false">@string/common_weather_text_dry</string>
    <string name="hko_weather_text_humid" translatable="false">@string/common_weather_text_humid</string>
    <string name="hko_weather_text_fog" translatable="false">@string/weather_kind_fog</string>
    <string name="hko_weather_text_mist" translatable="false">@string/common_weather_text_mist</string>
    <string name="hko_weather_text_haze" translatable="false">@string/weather_kind_haze</string>
    <string name="hko_weather_text_hot" translatable="false">@string/common_weather_text_hot</string>
    <string name="hko_weather_text_warm" translatable="false">@string/common_weather_text_warm</string>
    <string name="hko_weather_text_cool" translatable="false">@string/common_weather_text_cool</string>
    <string name="hko_weather_text_cold" translatable="false">@string/common_weather_text_cold</string>
    <string name="hko_warning_text_tropical_cyclone_1" translatable="false">Standby Signal No. 1</string>
    <string name="hko_warning_text_tropical_cyclone_3" translatable="false">Strong Wind Signal No. 3</string>
    <string name="hko_warning_text_tropical_cyclone_8_northeast" translatable="false">No. 8 Northeast Gale or Storm Signal</string>
    <string name="hko_warning_text_tropical_cyclone_8_northwest" translatable="false">No. 8 Northwest Gale or Storm Signal</string>
    <string name="hko_warning_text_tropical_cyclone_8_southeast" translatable="false">No. 8 Southeast Gale or Storm Signal</string>
    <string name="hko_warning_text_tropical_cyclone_8_southwest" translatable="false">No. 8 Southwest Gale or Storm Signal</string>
    <string name="hko_warning_text_tropical_cyclone_9" translatable="false">Increasing Gale or Storm Signal No. 9</string>
    <string name="hko_warning_text_tropical_cyclone_10" translatable="false">Hurricane Signal No. 10</string>
    <string name="hko_warning_text_tropical_cyclone_pre_8" translatable="false">Pre-8 Tropical Cyclone Special Announcement</string>
    <string name="hko_warning_text_rainstorm_amber" translatable="false">Amber Rainstorm Warning Signal</string>
    <string name="hko_warning_text_rainstorm_red" translatable="false">Red Rainstorm Warning Signal</string>
    <string name="hko_warning_text_rainstorm_black" translatable="false">Black Rainstorm Warning Signal</string>
    <string name="hko_warning_text_fire_yellow" translatable="false">Yellow Fire Danger Warning</string>
    <string name="hko_warning_text_fire_red" translatable="false">Red Fire Danger Warning</string>
    <string name="hko_warning_text_thunderstorm" translatable="false">Thunderstorm Warning</string>
    <string name="hko_warning_text_flooding_northern_nt" translatable="false">Special Announcement on Flooding in Northern New Territories</string>
    <string name="hko_warning_text_landslip" translatable="false">Landslip Warning</string>
    <string name="hko_warning_text_strong_monsoon" translatable="false">Strong Monsoon Signal</string>
    <string name="hko_warning_text_frost" translatable="false">Frost Warning</string>
    <string name="hko_warning_text_cold" translatable="false">Cold Weather Warning</string>
    <string name="hko_warning_text_very_hot" translatable="false">Very Hot Weather Warning</string>
    <string name="hko_warning_text_tsunami" translatable="false">Tsunami Warning</string>
    <string name="hko_warning_text_fire_yellow_description" translatable="false">The Fire Danger Warning is Yellow and the fire risk is High.</string>
    <string name="hko_warning_text_fire_red_description" translatable="false">The Fire Danger Warning is Red and the fire risk is Extreme.</string>
    <!-- Warning texts for SMG -->
    <string name="smg_warning_text_tropical_cyclone_1" translatable="false">Signal No.1</string>
    <string name="smg_warning_text_tropical_cyclone_3" translatable="false">Signal No.3</string>
    <string name="smg_warning_text_tropical_cyclone_8_northeast" translatable="false">Signal No.8NE</string>
    <string name="smg_warning_text_tropical_cyclone_8_northwest" translatable="false">Signal No.8NW</string>
    <string name="smg_warning_text_tropical_cyclone_8_southeast" translatable="false">Signal No.8SE</string>
    <string name="smg_warning_text_tropical_cyclone_8_southwest" translatable="false">Signal No.8SW</string>
    <string name="smg_warning_text_tropical_cyclone_9" translatable="false">Signal No.9</string>
    <string name="smg_warning_text_tropical_cyclone_10" translatable="false">Signal No.10</string>
    <string name="smg_warning_text_rainstorm_yellow" translatable="false">Yellow Rainstorm Warning Signal</string>
    <string name="smg_warning_text_rainstorm_red" translatable="false">Red Rainstorm Warning Signal</string>
    <string name="smg_warning_text_rainstorm_black" translatable="false">Black Rainstorm Warning Signal</string>
    <string name="smg_warning_text_strong_monsoon" translatable="false">Strong Monsoon Signal</string>
    <string name="smg_warning_text_thunderstorm" translatable="false">Thunderstorm Warning</string>
    <string name="smg_warning_text_storm_surge_blue" translatable="false">Blue Storm Surge Warning</string>
    <string name="smg_warning_text_storm_surge_yellow" translatable="false">Yellow Storm Surge Warning</string>
    <string name="smg_warning_text_storm_surge_orange" translatable="false">Orange Storm Surge Warning</string>
    <string name="smg_warning_text_storm_surge_red" translatable="false">Red Storm Surge Warning</string>
    <string name="smg_warning_text_storm_surge_black" translatable="false">Black Storm Surge Warning</string>
    <string name="smg_warning_text_tsunami" translatable="false">Tsunami Warning Signal</string>
    <!-- Warning texts for MeteoLux -->
    <string name="meteolux_warning_text_wind" translatable="false">Wind warning</string>
    <string name="meteolux_warning_text_rain" translatable="false">Rain warning</string>
    <string name="meteolux_warning_text_snow" translatable="false">Snow warning</string>
    <string name="meteolux_warning_text_black_ice" translatable="false">Black ice warning</string>
    <string name="meteolux_warning_text_thunderstorm" translatable="false">Thunderstorm warning</string>
    <string name="meteolux_warning_text_heat" translatable="false">Heat warning</string>
    <string name="meteolux_warning_text_cold" translatable="false">Cold warning</string>
    <string name="meteolux_warning_text_flood" translatable="false">Flood warning*</string>
    <string name="meteolux_warning_text_ozone" translatable="false">Ozone in the air*</string>
    <string name="meteolux_warning_text_pm" translatable="false">Particle pollution (PM) in the air*</string>
    <string name="meteolux_warning_text_level_2" translatable="false">Potential danger</string>
    <string name="meteolux_warning_text_level_3" translatable="false">Danger</string>
    <string name="meteolux_warning_text_level_4" translatable="false">Significant danger</string>
    <!-- Warning texts for JMA -->
    <string name="jma_warning_text_heavy_rain_emergency" translatable="false">Heavy rain emergency warning</string>
    <string name="jma_warning_text_heavy_rain_warning" translatable="false">Heavy rain warning</string>
    <string name="jma_warning_text_heavy_rain_advisory" translatable="false">Heavy rain advisory</string>
    <string name="jma_warning_text_flood_warning" translatable="false">Flood warning</string>
    <string name="jma_warning_text_flood_advisory" translatable="false">Flood advisory</string>
    <string name="jma_warning_text_storm_emergency" translatable="false">Storm emergency warning</string>
    <string name="jma_warning_text_storm_warning" translatable="false">Storm warning</string>
    <string name="jma_warning_text_gale_advisory" translatable="false">Gale advisory</string>
    <string name="jma_warning_text_snowstorm_emergency" translatable="false">Snowstorm emergency warning</string>
    <string name="jma_warning_text_snowstorm_warning" translatable="false">Snowstorm warning</string>
    <string name="jma_warning_text_gale_and_snow_advisory" translatable="false">Gale and snow advisory</string>
    <string name="jma_warning_text_heavy_snow_emergency" translatable="false">Heavy snow emergency warning</string>
    <string name="jma_warning_text_heavy_snow_warning" translatable="false">Heavy snow warning</string>
    <string name="jma_warning_text_heavy_snow_advisory" translatable="false">Heavy snow advisory</string>
    <string name="jma_warning_text_high_wave_emergency" translatable="false">High wave emergency warning</string>
    <string name="jma_warning_text_high_wave_warning" translatable="false">High wave warning</string>
    <string name="jma_warning_text_high_wave_advisory" translatable="false">High wave advisory</string>
    <string name="jma_warning_text_storm_surge_emergency" translatable="false">Storm surge emergency warning</string>
    <string name="jma_warning_text_storm_surge_warning" translatable="false">Storm surge warning</string>
    <string name="jma_warning_text_storm_surge_advisory" translatable="false">Storm surge advisory</string>
    <string name="jma_warning_text_thunderstorm_advisory" translatable="false">Thunderstorm advisory</string>
    <string name="jma_warning_text_snow_melting_advisory" translatable="false">Snow melting advisory</string>
    <string name="jma_warning_text_dense_fog_advisory" translatable="false">Dense fog advisory</string>
    <string name="jma_warning_text_dry_air_advisory" translatable="false">Dry air advisory</string>
    <string name="jma_warning_text_avalanche_advisory" translatable="false">Avalanche advisory</string>
    <string name="jma_warning_text_low_temperature_advisory" translatable="false">Low temperature advisory</string>
    <string name="jma_warning_text_frost_advisory" translatable="false">Frost advisory</string>
    <string name="jma_warning_text_ice_accretion_advisory" translatable="false">Ice accretion advisory</string>
    <string name="jma_warning_text_snow_accretion_advisory" translatable="false">Snow accretion advisory</string>
    <!-- Weather texts for NWS -->
    <string name="nws_weather_text_freezing_spray" translatable="false">Freezing spray</string>
    <string name="nws_weather_text_ice_crystals" translatable="false">Ice crystals</string>
    <string name="nws_weather_text_ice_fog" translatable="false">Ice fog</string>
    <string name="nws_weather_text_volcanic_ash" translatable="false">Volcanic ash</string>
    <string name="nws_weather_text_waterspout" translatable="false">Waterspout</string>
    <string name="nws_weather_text_intensity_light" translatable="false">Light %s</string>
    <string name="nws_weather_text_intensity_heavy" translatable="false">Heavy %s</string>
    <string name="nws_weather_text_coverage_areas_of" translatable="false">Areas of %s</string>
    <string name="nws_weather_text_coverage_brief" translatable="false">Brief %s</string>
    <string name="nws_weather_text_coverage_chance_of" translatable="false">Chance of %s</string>
    <string name="nws_weather_text_coverage_few" translatable="false">A few %s</string>
    <string name="nws_weather_text_coverage_frequent" translatable="false">Frequent %s</string>
    <string name="nws_weather_text_coverage_intermittent" translatable="false">Intermittent %s</string>
    <string name="nws_weather_text_coverage_isolated" translatable="false">Isolated %s</string>
    <string name="nws_weather_text_coverage_likely" translatable="false">%s likely</string>
    <string name="nws_weather_text_coverage_numerous" translatable="false">Numerous %s</string>
    <string name="nws_weather_text_coverage_occasional" translatable="false">Occasional %s</string>
    <string name="nws_weather_text_coverage_patchy" translatable="false">Patchy %s</string>
    <string name="nws_weather_text_coverage_periods_of" translatable="false">Periods of %s</string>
    <string name="nws_weather_text_coverage_scattered" translatable="false">Scattered %s</string>
    <string name="nws_weather_text_coverage_slight_chance_of" translatable="false">Slight chance of %s</string>
    <string name="nws_weather_text_coverage_widespread" translatable="false">Widespread %s</string>
    <!-- "Mainly clear and breezy" -->
    <string name="nws_weather_text_condition_and_wind" translatable="false">%s and %s</string>
    <string name="nws_weather_text_wind_high_wind" translatable="false">High Wind</string>
    <string name="nws_weather_text_wind_very_windy" translatable="false">Very Windy</string>
    <string name="nws_weather_text_wind_windy" translatable="false">Windy</string>
    <string name="nws_weather_text_wind_breezy" translatable="false">Breezy</string>
    <!-- "Heavy rain with large hail" -->
    <string name="nws_weather_text_condition_with_attribute" translatable="false">%s with %s</string>
    <string name="nws_weather_text_attribute_wind_damaging" translatable="false">Damaging Wind</string>
    <string name="nws_weather_text_attribute_flooding" translatable="false">Flooding</string>
    <string name="nws_weather_text_attribute_wind_gusty" translatable="false">Gusty Wind</string>
    <string name="nws_weather_text_attribute_hail_large" translatable="false">Large Hail</string>
    <string name="nws_weather_text_attribute_hail_small" translatable="false">Small Hail</string>
    <string name="nws_weather_text_separator_and" translatable="false"> and </string>
    <!-- Do NOT translate everything below -->
    <!-- Widgets -->
    <string name="widget_custom_subtitle_keyword_cw" translatable="false">$cw$</string>
    <string name="widget_custom_subtitle_keyword_ct" translatable="false">$ct$</string>
    <string name="widget_custom_subtitle_keyword_ctd" translatable="false">$ctd$</string>
    <string name="widget_custom_subtitle_keyword_at" translatable="false">$at$</string>
    <string name="widget_custom_subtitle_keyword_atd" translatable="false">$atd$</string>
    <string name="widget_custom_subtitle_keyword_cwd" translatable="false">$cwd$</string>
    <string name="widget_custom_subtitle_keyword_caqi" translatable="false">$caqi$</string>
    <string name="widget_custom_subtitle_keyword_cuv" translatable="false">$cuv$</string>
    <string name="widget_custom_subtitle_keyword_ch" translatable="false">$ch$</string>
    <string name="widget_custom_subtitle_keyword_cdp" translatable="false">$cdp$</string>
    <string name="widget_custom_subtitle_keyword_cps" translatable="false">$cps$</string>
    <string name="widget_custom_subtitle_keyword_cv" translatable="false">$cv$</string>
    <string name="widget_custom_subtitle_keyword_al" translatable="false">$al$</string>
    <string name="widget_custom_subtitle_keyword_als" translatable="false">$als$</string>
    <string name="widget_custom_subtitle_keyword_l" translatable="false">$l$</string>
    <string name="widget_custom_subtitle_keyword_lat" translatable="false">$lat$</string>
    <string name="widget_custom_subtitle_keyword_lon" translatable="false">$lon$</string>
    <string name="widget_custom_subtitle_keyword_ut" translatable="false">$ut$</string>
    <string name="widget_custom_subtitle_keyword_d" translatable="false">$d$</string>
    <string name="widget_custom_subtitle_keyword_lc" translatable="false">$lc$</string>
    <string name="widget_custom_subtitle_keyword_w" translatable="false">$w$</string>
    <string name="widget_custom_subtitle_keyword_ws" translatable="false">$ws$</string>
    <string name="widget_custom_subtitle_keyword_dd" translatable="false">$dd$</string>
    <string name="widget_custom_subtitle_keyword_hd" translatable="false">$hd$</string>
    <string name="widget_custom_subtitle_keyword_enter" translatable="false">$enter$</string>
    <string name="widget_custom_subtitle_keyword_xdw" translatable="false">$0dw$, $1dw$, …, $4dw$</string>
    <string name="widget_custom_subtitle_keyword_xnw" translatable="false">$0nw$, $1nw$, …, $4nw$</string>
    <string name="widget_custom_subtitle_keyword_xdt" translatable="false">$0dt$, $1dt$, …, $4dt$</string>
    <string name="widget_custom_subtitle_keyword_xnt" translatable="false">$0nt$, $1nt$, …, $4nt$</string>
    <string name="widget_custom_subtitle_keyword_xdtd" translatable="false">$0dtd$, $1dtd$, …, $4dtd$</string>
    <string name="widget_custom_subtitle_keyword_xntd" translatable="false">$0ntd$, $1ntd$, …, $4ntd$</string>
    <string name="widget_custom_subtitle_keyword_xdp" translatable="false">$0dp$, $1dp$, …, $4dp$</string>
    <string name="widget_custom_subtitle_keyword_xnp" translatable="false">$0np$, $1np$, …, $4np$</string>
    <string name="widget_custom_subtitle_keyword_xdwd" translatable="false">$0dwd$, $1dwd$, …, $4dwd$</string>
    <string name="widget_custom_subtitle_keyword_xnwd" translatable="false">$0nwd$, $1nwd$, …, $4nwd$</string>
    <string name="widget_custom_subtitle_keyword_xaqi" translatable="false">$0aqi$, $1aqi$, …, $4aqi$</string>
    <string name="widget_custom_subtitle_keyword_xpis" translatable="false">$0pis$, $1pis$, …, $4pis$</string>
    <string name="widget_custom_subtitle_keyword_xsr" translatable="false">$0sr$, $1sr$, …, $4sr$</string>
    <string name="widget_custom_subtitle_keyword_xss" translatable="false">$0ss$, $1ss$, …, $4ss$</string>
    <string name="widget_custom_subtitle_keyword_xmr" translatable="false">$0mr$, $1mr$, …, $4mr$</string>
    <string name="widget_custom_subtitle_keyword_xms" translatable="false">$0ms$, $1ms$, …, $4ms$</string>
    <string name="widget_custom_subtitle_keyword_xmp" translatable="false">$0mp$, $1mp$, …, $4mp$</string>
    <!-- Tags -->
    <string name="tag_temperature" translatable="false">@string/conditions</string>
    <string name="tag_wind" translatable="false">@string/wind</string>
    <string name="tag_precipitation" translatable="false">@string/precipitation</string>
    <string name="tag_aqi" translatable="false">@string/air_quality</string>
    <string name="tag_uv" translatable="false">@string/uv_index</string>
    <string name="tag_feels_like" translatable="false">@string/temperature_feels_like</string>
    <string name="tag_humidity_dew_point" translatable="false">@string/humidity_dew_point</string>
    <string name="tag_pressure" translatable="false">@string/pressure</string>
    <string name="tag_cloud_cover" translatable="false">@string/cloud_cover</string>
    <string name="tag_visibility" translatable="false">@string/visibility</string>
    <string name="tag_sunshine" translatable="false">@string/sunshine</string>
    <!-- Keyword shown when data is missing, for example requesting humidity in widgets but data doesn’t exist -->
    <string name="null_data_text">N/A</string>
    <string name="colon_separator" translatable="false">:\u0020</string>
    <string name="parenthesis" translatable="false">(%s)</string>
    <string name="comma_separator" translatable="false">,\u0020</string>
</resources>
