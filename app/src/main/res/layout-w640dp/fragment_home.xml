<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/fragment_root"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <org.breezyweather.ui.common.widgets.SwipeSwitchLayout
        android:id="@+id/switch_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <org.breezyweather.ui.common.widgets.insets.FitSystemBarSwipeRefreshLayout
            android:id="@+id/refresh_layout"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <org.breezyweather.ui.main.widgets.FitTabletRecyclerView
                android:id="@+id/recycler_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scrollbars="none"
                android:clipToPadding="false"
                android:descendantFocusability="blocksDescendants" />

        </org.breezyweather.ui.common.widgets.insets.FitSystemBarSwipeRefreshLayout>

    </org.breezyweather.ui.common.widgets.SwipeSwitchLayout>

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBar"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@android:color/transparent"
        app:elevation="0dp"
        app:liftOnScrollColor="@android:color/transparent">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@android:color/transparent"
            android:elevation="0dp"
            app:navigationIcon="@drawable/ic_menu"
            app:navigationContentDescription="@string/action_location_list"
            app:navigationIconTint="@android:color/white"
            app:titleTextColor="@android:color/white"
            app:titleTextAppearance="?attr/textAppearanceHeadline6" />

        <org.breezyweather.ui.main.widgets.TextRelativeClock
            android:id="@+id/refreshTimeText"
            android:textSize="@dimen/content_text_size"
            android:maxLines="1"
            android:ellipsize="end"
            android:drawablePadding="6dp"
            android:gravity="center_vertical"
            android:drawableStart="@drawable/ic_time"
            style="@style/large_title_text"
            tools:text="1 minute ago"
            tools:ignore="RelativeOverlap,RtlHardcoded" />

    </com.google.android.material.appbar.AppBarLayout>

    <org.breezyweather.ui.common.widgets.InkPageIndicator
        android:id="@+id/indicator"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/normal_margin"
        android:alpha="0"
        android:layout_gravity="bottom|center_horizontal"
        app:layout_behavior="org.breezyweather.ui.common.behaviors.FloatingAboveSnackbarBehavior"
        app:dotDiameter="8dp"
        app:dotGap="8dp"
        app:animationDuration="300" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
