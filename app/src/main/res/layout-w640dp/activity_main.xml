<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <org.breezyweather.ui.common.widgets.DrawerLayout
        android:id="@+id/drawerLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:unfold="true">

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragment_drawer"
            android:name="org.breezyweather.ui.main.fragments.ManagementFragment"
            android:tag="fragment_management_w600dp_land"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:elevation="2dp"
            tools:ignore="UnusedAttribute" />

        <androidx.fragment.app.FragmentContainerView
            android:id="@+id/fragment_home"
            android:name="org.breezyweather.ui.main.fragments.HomeFragment"
            android:tag="fragment_main_w600dp_land"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            tools:ignore="UnusedAttribute" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/per_location_settings"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/location_permission_dialog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <androidx.compose.ui.platform.ComposeView
            android:id="@+id/refresh_error_dialog"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </org.breezyweather.ui.common.widgets.DrawerLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
