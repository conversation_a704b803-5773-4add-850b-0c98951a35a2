<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_multi_city_horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@android:color/black">

    <LinearLayout
        android:id="@+id/widget_multi_city_horizontal_weather"
        android:orientation="horizontal"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true"
        tools:ignore="DisableBaselineAlignment">

        <LinearLayout
            android:id="@+id/widget_multi_city_horizontal_weather_1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            tools:ignore="RtlHardcoded">

            <TextView
                android:id="@+id/widget_multi_city_horizontal_title_1"
                android:text="@string/widget_ellipsis"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:layout_margin="@dimen/normal_margin"
                style="@style/widget_title_text"
                tools:text="Guayaquil" />

            <ImageView
                android:id="@+id/widget_multi_city_horizontal_icon_1"
                android:layout_width="@dimen/widget_little_weather_icon_size"
                android:layout_height="@dimen/widget_little_weather_icon_size"
                android:layout_marginStart="@dimen/normal_margin"
                android:layout_marginEnd="@dimen/normal_margin"
                android:src="@drawable/weather_clear_day"
                tools:ignore="ContentDescription,RtlHardcoded" />

            <TextView
                android:id="@+id/widget_multi_city_horizontal_content_1"
                android:text="@string/widget_ellipsis"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:layout_margin="@dimen/normal_margin"
                style="@style/widget_content_text"
                tools:text="9°/1°" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/widget_multi_city_horizontal_weather_2"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            tools:ignore="RtlHardcoded">

            <TextView
                android:id="@+id/widget_multi_city_horizontal_title_2"
                android:text="@string/widget_ellipsis"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:layout_margin="@dimen/normal_margin"
                style="@style/widget_title_text"
                tools:text="Quayaquil" />

            <ImageView
                android:id="@+id/widget_multi_city_horizontal_icon_2"
                android:layout_width="@dimen/widget_little_weather_icon_size"
                android:layout_height="@dimen/widget_little_weather_icon_size"
                android:layout_marginStart="@dimen/normal_margin"
                android:layout_marginEnd="@dimen/normal_margin"
                android:src="@drawable/weather_clear_day"
                tools:ignore="ContentDescription,RtlHardcoded" />

            <TextView
                android:id="@+id/widget_multi_city_horizontal_content_2"
                android:text="@string/widget_ellipsis"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:layout_margin="@dimen/normal_margin"
                style="@style/widget_content_text"
                tools:text="9°/1°" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/widget_multi_city_horizontal_weather_3"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical"
            android:gravity="center_horizontal"
            tools:ignore="RtlHardcoded">

            <TextView
                android:id="@+id/widget_multi_city_horizontal_title_3"
                android:text="@string/widget_ellipsis"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:layout_margin="@dimen/normal_margin"
                style="@style/widget_title_text"
                tools:text="Quayaquil" />

            <ImageView
                android:id="@+id/widget_multi_city_horizontal_icon_3"
                android:layout_width="@dimen/widget_little_weather_icon_size"
                android:layout_height="@dimen/widget_little_weather_icon_size"
                android:layout_marginStart="@dimen/normal_margin"
                android:layout_marginEnd="@dimen/normal_margin"
                android:src="@drawable/weather_clear_day"
                tools:ignore="ContentDescription,RtlHardcoded" />

            <TextView
                android:id="@+id/widget_multi_city_horizontal_content_3"
                android:text="@string/widget_ellipsis"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:layout_margin="@dimen/normal_margin"
                style="@style/widget_content_text"
                tools:text="9°/1°" />

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
