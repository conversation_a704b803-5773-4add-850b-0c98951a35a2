<?xml version="1.0" encoding="utf-8"?>
<org.breezyweather.ui.common.widgets.slidingItem.SlidingItemContainerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:apps="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_card_display_container"
    android:layout_width="match_parent"
    android:layout_height="56dp"
    android:foreground="@drawable/selectable_item_background"
    android:stateListAnimator="@animator/touch_raise"
    apps:iconResStart="@drawable/ic_delete"
    apps:iconResEnd="@drawable/ic_delete"
    apps:backgroundColorStart="?attr/colorErrorContainer"
    apps:backgroundColorEnd="?attr/colorErrorContainer"
    apps:tintColorStart="?attr/colorOnErrorContainer"
    apps:tintColorEnd="?attr/colorOnErrorContainer"
    tools:ignore="UnusedAttribute">

    <RelativeLayout
        android:id="@+id/item_card_display"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/colorSurface">

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/item_card_display_sortButton"
            android:src="@drawable/ic_drag"
            android:tint="?attr/colorPrimary"
            android:layout_centerVertical="true"
            android:layout_alignParentStart="true"
            android:contentDescription="@string/settings_items_drag_to_sort"
            style="@style/material_image_button" />

        <androidx.appcompat.widget.AppCompatImageButton
            android:id="@+id/item_card_display_deleteBtn"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:contentDescription="@string/settings_item_delete"
            android:src="@drawable/ic_close"
            android:tint="?attr/colorPrimary"
            style="@style/material_image_button" />

        <TextView
            android:id="@+id/item_card_display_title"
            android:layout_centerVertical="true"
            android:layout_toStartOf="@id/item_card_display_deleteBtn"
            android:layout_toEndOf="@id/item_card_display_sortButton"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="?attr/colorOnSurface"
            style="@style/title_text"
            tools:text="@string/ephemeris"
            tools:ignore="RtlHardcoded" />

    </RelativeLayout>

</org.breezyweather.ui.common.widgets.slidingItem.SlidingItemContainerLayout>
