<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_clock_day_week"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@android:color/black">

    <LinearLayout
        android:id="@+id/widget_clock_day_week_weather"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="RtlHardcoded">

            <RelativeLayout
                android:id="@+id/widget_clock_day_week_clock_lightContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true">

                <TextClock
                    android:id="@+id/widget_clock_day_week_clock_light"
                    android:fontFamily="sans-serif-light"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginEnd="@dimen/little_margin"
                    android:shadowColor="@android:color/black"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    style="@style/widget_text_clock"
                    tools:text="9:10"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

                <TextClock
                    android:id="@+id/widget_clock_day_week_clock_aa_light"
                    android:layout_toEndOf="@id/widget_clock_day_week_clock_light"
                    android:layout_alignBaseline="@id/widget_clock_day_week_clock_light"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    style="@style/widget_text_clock_aa"
                    tools:text="AM"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/widget_clock_day_week_clock_normalContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:visibility="gone"
                tools:visibility="visible">

                <TextClock
                    android:id="@+id/widget_clock_day_week_clock_normal"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginEnd="@dimen/little_margin"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    style="@style/widget_text_clock"
                    tools:text="9:10"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

                <TextClock
                    android:id="@+id/widget_clock_day_week_clock_aa_normal"
                    android:layout_toEndOf="@id/widget_clock_day_week_clock_normal"
                    android:layout_alignBaseline="@id/widget_clock_day_week_clock_normal"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    style="@style/widget_text_clock_aa"
                    tools:text="AM"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/widget_clock_day_week_clock_blackContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:visibility="gone"
                tools:visibility="visible">

                <TextClock
                    android:id="@+id/widget_clock_day_week_clock_black"
                    android:fontFamily="sans-serif-black"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginEnd="@dimen/little_margin"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    style="@style/widget_text_clock"
                    tools:text="9:10"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

                <TextClock
                    android:id="@+id/widget_clock_day_week_clock_aa_black"
                    android:layout_toEndOf="@id/widget_clock_day_week_clock_black"
                    android:layout_alignBaseline="@id/widget_clock_day_week_clock_black"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    style="@style/widget_text_clock_aa"
                    tools:text="AM"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

            </RelativeLayout>

            <ImageView
                android:id="@+id/widget_clock_day_week_icon"
                android:layout_width="@dimen/widget_current_weather_icon_size"
                android:layout_height="@dimen/widget_current_weather_icon_size"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/little_margin"
                android:layout_marginEnd="@dimen/normal_margin"
                android:src="@drawable/weather_cloudy"
                tools:ignore="ContentDescription,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp">

            <TextClock
                android:id="@+id/widget_clock_day_week_title"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/large_margin"
                android:textSize="@dimen/widget_content_text_size"
                android:maxLines="1"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock"
                tools:text="Sun 4 Nov"
                tools:ignore="RtlHardcoded" />

            <TextView
                android:id="@+id/widget_clock_day_week_alternate_calendar"
                android:layout_toEndOf="@id/widget_clock_day_week_title"
                android:layout_centerVertical="true"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="@dimen/little_margin"
                android:text=""
                android:textSize="@dimen/widget_content_text_size"
                android:maxLines="1"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_content_text"
                tools:text=" – 九月十八"
                tools:ignore="RtlHardcoded" />

            <TextView
                android:id="@+id/widget_clock_day_week_subtitle"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/little_margin"
                android:layout_marginEnd="@dimen/large_margin"
                android:text="@string/widget_ellipsis"
                android:textSize="@dimen/widget_content_text_size"
                android:maxLines="1"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_content_text"
                tools:text="Surabaya 8 °C"
                tools:ignore="RtlHardcoded" />

        </RelativeLayout>

        <LinearLayout
            android:orientation="horizontal"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/little_margin"
            tools:ignore="DisableBaselineAlignment">

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/widget_clock_day_week_week_1"
                    android:text="@string/widget_refreshing"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="Today" />

                <ImageView
                    android:id="@+id/widget_clock_day_week_icon_1"
                    android:layout_width="@dimen/widget_little_weather_icon_size"
                    android:layout_height="@dimen/widget_little_weather_icon_size"
                    android:layout_below="@id/widget_clock_day_week_week_1"
                    android:layout_centerHorizontal="true"
                    android:padding="2dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/weather_cloudy"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/widget_clock_day_week_temp_1"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_below="@id/widget_clock_day_week_icon_1"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="9°/4°" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/widget_clock_day_week_week_2"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="Thu" />

                <ImageView
                    android:id="@+id/widget_clock_day_week_icon_2"
                    android:layout_width="@dimen/widget_little_weather_icon_size"
                    android:layout_height="@dimen/widget_little_weather_icon_size"
                    android:layout_below="@id/widget_clock_day_week_week_2"
                    android:layout_centerHorizontal="true"
                    android:padding="2dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/weather_cloudy"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/widget_clock_day_week_temp_2"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_below="@id/widget_clock_day_week_icon_2"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="9°/4°" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/widget_clock_day_week_week_3"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="Fri" />

                <ImageView
                    android:id="@+id/widget_clock_day_week_icon_3"
                    android:layout_width="@dimen/widget_little_weather_icon_size"
                    android:layout_height="@dimen/widget_little_weather_icon_size"
                    android:layout_below="@id/widget_clock_day_week_week_3"
                    android:layout_centerHorizontal="true"
                    android:padding="2dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/weather_cloudy"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/widget_clock_day_week_temp_3"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_below="@id/widget_clock_day_week_icon_3"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="9°/4°" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/widget_clock_day_week_week_4"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="Sat" />

                <ImageView
                    android:id="@+id/widget_clock_day_week_icon_4"
                    android:layout_width="@dimen/widget_little_weather_icon_size"
                    android:layout_height="@dimen/widget_little_weather_icon_size"
                    android:layout_below="@id/widget_clock_day_week_week_4"
                    android:layout_centerHorizontal="true"
                    android:padding="2dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/weather_cloudy"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/widget_clock_day_week_temp_4"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_below="@id/widget_clock_day_week_icon_4"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="9°/4°" />

            </RelativeLayout>

            <RelativeLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1">

                <TextView
                    android:id="@+id/widget_clock_day_week_week_5"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_alignParentTop="true"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="Sun" />

                <ImageView
                    android:id="@+id/widget_clock_day_week_icon_5"
                    android:layout_width="@dimen/widget_little_weather_icon_size"
                    android:layout_height="@dimen/widget_little_weather_icon_size"
                    android:layout_below="@id/widget_clock_day_week_week_5"
                    android:layout_centerHorizontal="true"
                    android:padding="2dp"
                    android:layout_marginTop="2dp"
                    android:layout_marginBottom="2dp"
                    android:src="@drawable/weather_cloudy"
                    tools:src="@drawable/weather_clear_day"
                    tools:ignore="ContentDescription" />

                <TextView
                    android:id="@+id/widget_clock_day_week_temp_5"
                    android:text="@string/widget_ellipsis"
                    android:textSize="@dimen/widget_content_text_size"
                    android:maxLines="1"
                    android:shadowColor="@color/colorWidgetTextShadow"
                    android:shadowDx="1"
                    android:shadowDy="1"
                    android:shadowRadius="1"
                    android:layout_below="@id/widget_clock_day_week_icon_5"
                    android:layout_centerHorizontal="true"
                    style="@style/widget_content_text"
                    tools:text="9°/4°" />

            </RelativeLayout>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>
