<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_day"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@android:color/black">

    <RelativeLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingEnd="@dimen/normal_margin"
        android:layout_centerVertical="true"
        tools:ignore="RtlHardcoded,RtlSymmetry,UselessParent">

        <LinearLayout
            android:id="@+id/widget_day_weather"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentEnd="true"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:id="@+id/widget_day_icon"
                android:layout_width="@dimen/widget_little_weather_icon_size"
                android:layout_height="@dimen/widget_little_weather_icon_size"
                android:layout_marginEnd="@dimen/little_margin"
                android:src="@drawable/weather_cloudy"
                tools:src="@drawable/weather_clear_day"
                tools:ignore="ContentDescription,RtlHardcoded" />

            <TextView
                android:id="@+id/widget_day_title"
                android:text="@string/widget_ellipsis"
                android:textSize="@dimen/widget_design_title_text_size"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                android:maxLines="1"
                style="@style/widget_large_title_text"
                tools:text="9 °C"
                tools:ignore="RtlHardcoded" />

        </LinearLayout>

        <TextClock
            android:id="@+id/widget_day_time"
            android:text="@string/widget_ellipsis"
            android:textSize="@dimen/widget_subtitle_text_size"
            android:textAlignment="viewEnd"
            android:shadowColor="@color/colorWidgetTextShadow"
            android:shadowDx="1"
            android:shadowDy="1"
            android:shadowRadius="1"
            android:layout_alignParentEnd="true"
            android:layout_below="@id/widget_day_weather"
            style="@style/widget_text_clock"
            tools:text="Sunday, 29 December"
            tools:ignore="RtlHardcoded" />

    </RelativeLayout>

</RelativeLayout>
