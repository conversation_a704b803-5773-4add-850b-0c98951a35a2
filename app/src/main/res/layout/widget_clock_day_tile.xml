<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_clock_day"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@android:color/black">

    <LinearLayout
        android:id="@+id/widget_clock_day_weather"
        android:layout_centerInParent="true"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <RelativeLayout
            android:id="@+id/widget_clock_day_clock_lightContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextClock
                android:id="@+id/widget_clock_day_clock_light"
                android:fontFamily="sans-serif-light"
                android:layout_centerInParent="true"
                android:layout_marginStart="@dimen/little_margin"
                android:layout_marginEnd="@dimen/little_margin"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock"
                tools:text="9:10"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

            <TextClock
                android:id="@+id/widget_clock_day_clock_aa_light"
                android:layout_toEndOf="@id/widget_clock_day_clock_light"
                android:layout_alignBaseline="@id/widget_clock_day_clock_light"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock_aa"
                tools:text="AM"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/widget_clock_day_clock_normalContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <TextClock
                android:id="@+id/widget_clock_day_clock_normal"
                android:layout_centerInParent="true"
                android:layout_marginStart="@dimen/little_margin"
                android:layout_marginEnd="@dimen/little_margin"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock"
                tools:text="9:10"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

            <TextClock
                android:id="@+id/widget_clock_day_clock_aa_normal"
                android:layout_toEndOf="@id/widget_clock_day_clock_normal"
                android:layout_alignBaseline="@id/widget_clock_day_clock_normal"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock_aa"
                tools:text="AM"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/widget_clock_day_clock_blackContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <TextClock
                android:id="@+id/widget_clock_day_clock_black"
                android:fontFamily="sans-serif-black"
                android:layout_centerInParent="true"
                android:layout_marginStart="@dimen/little_margin"
                android:layout_marginEnd="@dimen/little_margin"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock"
                tools:text="9:10"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

            <TextClock
                android:id="@+id/widget_clock_day_clock_aa_black"
                android:layout_toEndOf="@id/widget_clock_day_clock_black"
                android:layout_alignBaseline="@id/widget_clock_day_clock_black"
                android:shadowColor="@color/colorWidgetTextShadow"
                android:shadowDx="1"
                android:shadowDy="1"
                android:shadowRadius="1"
                style="@style/widget_text_clock_aa"
                tools:text="AM"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/widget_clock_day_clock_analogContainer_auto"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <AnalogClock
                android:id="@+id/widget_clock_day_clock_analog_auto"
                android:layout_margin="@dimen/little_margin"
                android:layout_centerInParent="true"
                style="@style/widget_text_clock_analog"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/widget_clock_day_clock_analogContainer_light"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <AnalogClock
                android:id="@+id/widget_clock_day_clock_analog_light"
                android:layout_margin="@dimen/little_margin"
                android:layout_centerInParent="true"
                style="@style/widget_text_clock_analog.Light"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:id="@+id/widget_clock_day_clock_analogContainer_dark"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="gone"
            tools:visibility="visible">

            <AnalogClock
                android:id="@+id/widget_clock_day_clock_analog_dark"
                android:layout_margin="@dimen/little_margin"
                android:layout_centerInParent="true"
                style="@style/widget_text_clock_analog.Dark"
                tools:ignore="RelativeOverlap,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <RelativeLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_centerInParent="true"
                android:layout_marginStart="@dimen/normal_margin"
                android:layout_marginEnd="@dimen/normal_margin"
                tools:ignore="UselessParent">

                <ImageView
                    android:id="@+id/widget_clock_day_icon"
                    android:layout_width="@dimen/widget_current_weather_icon_size"
                    android:layout_height="@dimen/widget_current_weather_icon_size"
                    android:layout_centerVertical="true"
                    android:src="@drawable/weather_cloudy"
                    tools:ignore="ContentDescription" />

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/little_margin"
                    android:layout_centerVertical="true"
                    android:layout_toEndOf="@id/widget_clock_day_icon"
                    tools:ignore="RtlHardcoded">

                    <TextView
                        android:id="@+id/widget_clock_day_title"
                        android:text="@string/widget_ellipsis"
                        android:maxLines="1"
                        android:textSize="@dimen/widget_content_text_size"
                        android:shadowColor="@color/colorWidgetTextShadow"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="1"
                        style="@style/widget_content_text"
                        tools:text="Partly cloudy 21 °C"
                        tools:ignore="RtlHardcoded" />

                    <TextView
                        android:id="@+id/widget_clock_day_subtitle"
                        android:text="@string/widget_ellipsis"
                        android:maxLines="1"
                        android:textSize="@dimen/widget_content_text_size"
                        android:shadowColor="@color/colorWidgetTextShadow"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="1"
                        style="@style/widget_content_text"
                        tools:text="23°/17°"
                        tools:ignore="RtlHardcoded" />

                    <TextView
                        android:id="@+id/widget_clock_day_time"
                        android:text="@string/widget_refreshing"
                        android:textSize="@dimen/widget_time_text_size"
                        android:shadowColor="@color/colorWidgetTextShadow"
                        android:shadowDx="1"
                        android:shadowDy="1"
                        android:shadowRadius="1"
                        style="@style/widget_content_text"
                        tools:text="San Francisco WEN 17:00"
                        tools:ignore="RtlHardcoded" />

                </LinearLayout>

            </RelativeLayout>

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>
