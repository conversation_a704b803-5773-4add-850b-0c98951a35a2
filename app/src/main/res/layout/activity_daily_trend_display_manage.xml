<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container"
    android:background="?attr/colorSurface"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <org.breezyweather.ui.common.widgets.insets.FitSystemBarAppBarLayout
            android:id="@+id/appBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_alignParentTop="true"
            style="@style/BreezyWeatherTheme.AppBarTheme">

            <com.google.android.material.appbar.MaterialToolbar
                android:id="@+id/toolbar"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                style="@style/BreezyWeatherTheme.ToolbarTheme"
                app:title="@string/settings_main_daily_trends_title"
                app:navigationIcon="@drawable/ic_toolbar_back"
                app:navigationContentDescription="@string/action_back" />

        </org.breezyweather.ui.common.widgets.insets.FitSystemBarAppBarLayout>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recyclerView"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_below="@id/appBar"
            android:clipToPadding="false"
            android:paddingBottom="128dp" />

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/bottomBar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:attr/colorBackground"
            android:layout_alignParentBottom="true">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/bottomRecyclerView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                tools:ignore="RtlSymmetry,UnusedAttribute" />

        </com.google.android.material.appbar.AppBarLayout>

    </RelativeLayout>

</androidx.coordinatorlayout.widget.CoordinatorLayout>
