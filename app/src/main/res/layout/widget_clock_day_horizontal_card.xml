<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/widget_clock_day"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:background="@android:color/black">

    <ImageView
        android:id="@+id/widget_clock_day_card"
        style="@style/widget_background_card"
        tools:ignore="ContentDescription" />

    <LinearLayout
        android:id="@+id/widget_clock_day_weather"
        android:orientation="vertical"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerInParent="true">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            tools:ignore="RtlHardcoded">

            <RelativeLayout
                android:id="@+id/widget_clock_day_clock_lightContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true">

                <TextClock
                    android:id="@+id/widget_clock_day_clock_light"
                    android:fontFamily="sans-serif-light"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginEnd="@dimen/little_margin"
                    android:layout_marginTop="-2dp"
                    android:layout_marginBottom="-2dp"
                    android:textSize="48dp"
                    style="@style/widget_text_clock"
                    tools:text="9:10"
                    tools:ignore="RelativeOverlap,RtlHardcoded,SpUsage" />

                <TextClock
                    android:id="@+id/widget_clock_day_clock_aa_light"
                    android:layout_toEndOf="@id/widget_clock_day_clock_light"
                    android:layout_alignBaseline="@id/widget_clock_day_clock_light"
                    style="@style/widget_text_clock_aa"
                    tools:text="AM"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/widget_clock_day_clock_normalContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:visibility="gone">

                <TextClock
                    android:id="@+id/widget_clock_day_clock_normal"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginEnd="@dimen/little_margin"
                    android:layout_marginTop="-2dp"
                    android:layout_marginBottom="-2dp"
                    android:textSize="48dp"
                    style="@style/widget_text_clock"
                    tools:text="9:10"
                    tools:ignore="RelativeOverlap,RtlHardcoded,SpUsage" />

                <TextClock
                    android:id="@+id/widget_clock_day_clock_aa_normal"
                    android:layout_toEndOf="@id/widget_clock_day_clock_normal"
                    android:layout_alignBaseline="@id/widget_clock_day_clock_normal"
                    style="@style/widget_text_clock_aa"
                    tools:text="AM"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

            </RelativeLayout>

            <RelativeLayout
                android:id="@+id/widget_clock_day_clock_blackContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:visibility="gone">

                <TextClock
                    android:id="@+id/widget_clock_day_clock_black"
                    android:fontFamily="sans-serif-black"
                    android:layout_alignParentStart="true"
                    android:layout_centerVertical="true"
                    android:layout_marginStart="@dimen/normal_margin"
                    android:layout_marginEnd="@dimen/little_margin"
                    android:layout_marginTop="-2dp"
                    android:layout_marginBottom="-2dp"
                    android:textSize="48dp"
                    style="@style/widget_text_clock"
                    tools:text="9:10"
                    tools:ignore="RelativeOverlap,RtlHardcoded,SpUsage" />

                <TextClock
                    android:id="@+id/widget_clock_day_clock_aa_black"
                    android:layout_toEndOf="@id/widget_clock_day_clock_black"
                    android:layout_alignBaseline="@id/widget_clock_day_clock_black"
                    style="@style/widget_text_clock_aa"
                    tools:text="AM"
                    tools:ignore="RelativeOverlap,RtlHardcoded" />

            </RelativeLayout>

            <ImageView
                android:id="@+id/widget_clock_day_icon"
                android:layout_width="@dimen/widget_standard_weather_icon_size"
                android:layout_height="@dimen/widget_standard_weather_icon_size"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/little_margin"
                android:layout_marginEnd="@dimen/normal_margin"
                android:src="@drawable/weather_clear_day"
                tools:ignore="ContentDescription,RtlHardcoded" />

        </RelativeLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <TextClock
                android:id="@+id/widget_clock_day_title"
                android:layout_alignParentStart="true"
                android:layout_centerVertical="true"
                android:layout_marginStart="@dimen/large_margin"
                android:textSize="@dimen/widget_content_text_size"
                android:maxLines="1"
                style="@style/widget_text_clock"
                tools:text="Sun 4 Nov"
                tools:ignore="RtlHardcoded" />

            <TextView
                android:id="@+id/widget_clock_day_alternate_calendar"
                android:layout_toEndOf="@id/widget_clock_day_title"
                android:layout_centerVertical="true"
                android:layout_marginStart="2dp"
                android:layout_marginEnd="@dimen/little_margin"
                android:text=""
                android:textSize="@dimen/widget_content_text_size"
                android:maxLines="1"
                style="@style/widget_content_text"
                tools:text=" – 九月十八"
                tools:ignore="RtlHardcoded" />

            <TextView
                android:id="@+id/widget_clock_day_subtitle"
                android:layout_toEndOf="@id/widget_clock_day_alternate_calendar"
                android:layout_alignParentEnd="true"
                android:layout_centerVertical="true"
                android:layout_marginEnd="@dimen/large_margin"
                android:layout_width="match_parent"
                android:text="@string/widget_ellipsis"
                android:textSize="@dimen/widget_content_text_size"
                android:textAlignment="textEnd"
                android:ellipsize="start"
                android:singleLine="true"
                style="@style/widget_content_text"
                tools:text="Surabaya 8 °C"
                tools:ignore="RtlHardcoded" />

        </RelativeLayout>

    </LinearLayout>

</RelativeLayout>
