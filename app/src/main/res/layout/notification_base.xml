<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/notification_base"
    android:orientation="horizontal"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:layout_height="82dp">

    <ImageView
        android:id="@+id/notification_base_icon"
        android:scaleType="fitCenter"
        android:layout_width="42dp"
        android:layout_height="42dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:layout_marginStart="@dimen/little_margin"
        android:layout_marginEnd="@dimen/little_margin"
        tools:src="@drawable/weather_cloudy"
        tools:ignore="ContentDescription,RtlHardcoded" />

    <TextView
        android:id="@+id/notification_base_realtimeTemp"
        android:layout_toEndOf="@id/notification_base_icon"
        android:layout_centerVertical="true"
        android:textSize="@dimen/design_title_text_size"
        android:includeFontPadding="false"
        style="@style/notification_large_title_text"
        tools:text="3°"
        tools:ignore="RelativeOverlap,RtlHardcoded" />

    <LinearLayout
        android:id="@+id/notification_base_titleContainer"
        android:orientation="vertical"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_toEndOf="@id/notification_base_realtimeTemp"
        android:layout_marginStart="@dimen/little_margin"
        android:layout_centerVertical="true"
        tools:ignore="RtlHardcoded">

        <TextView
            android:id="@+id/notification_base_weather"
            android:text="@string/widget_refreshing"
            android:textSize="@dimen/title_text_size"
            android:includeFontPadding="false"
            android:layout_marginEnd="@dimen/little_margin"
            android:maxLines="1"
            android:ellipsize="end"
            style="@style/notification_title_text"
            tools:text="@string/weather_kind_partly_cloudy" />

        <TextView
            android:id="@+id/notification_base_aqiAndWind"
            android:text="@string/widget_ellipsis"
            android:textSize="@dimen/content_text_size"
            android:includeFontPadding="false"
            android:layout_marginEnd="@dimen/little_margin"
            android:maxLines="1"
            android:ellipsize="end"
            style="@style/notification_content_text"
            tools:text="Air quality – Fair" />

        <TextView
            android:id="@+id/notification_base_time"
            android:text="@string/widget_ellipsis"
            android:textSize="@dimen/widget_time_text_size"
            android:includeFontPadding="false"
            android:layout_marginEnd="@dimen/little_margin"
            android:maxLines="1"
            android:ellipsize="end"
            style="@style/notification_subtitle_text"
            tools:text="Prague"
            tools:ignore="RtlHardcoded,SmallSp" />

    </LinearLayout>

</RelativeLayout>
