<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/activity_widget_config"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/activity_widget_config_top"
        android:layout_width="match_parent"
        android:layout_height="360dp">

        <ImageView
            android:id="@+id/activity_widget_config_wall"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="centerCrop"
            android:src="?attr/colorPrimaryContainer"
            tools:ignore="contentDescription" />

        <FrameLayout
            android:id="@+id/activity_widget_config_widgetContainer"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:padding="@dimen/little_margin" />

    </FrameLayout>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/activity_widget_config_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:id="@+id/activity_widget_config_scrollView"
            android:scrollbars="vertical"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false">

            <LinearLayout
                android:id="@+id/activity_widget_config_scrollContainer"
                android:orientation="vertical"
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <RelativeLayout
                    android:id="@+id/activity_widget_config_viewStyleContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_viewStyleTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_view_style"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/activity_widget_config_styleSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown"
                        android:layout_toEndOf="@id/activity_widget_config_viewStyleTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:theme="@style/spinner_item"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_showCardContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_showCardTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_show_widget_card"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/activity_widget_config_showCardSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown"
                        android:layout_toEndOf="@id/activity_widget_config_showCardTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:theme="@style/spinner_item"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_cardAlphaContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_cardAlphaTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_show_widget_card_alpha"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/activity_widget_config_cardAlphaSlider"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@id/activity_widget_config_cardAlphaTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_hideSubtitleContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_hideSubtitleTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_hide_subtitle"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <Switch
                        android:id="@+id/activity_widget_config_hideSubtitleSwitch"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/normal_margin"
                        style="@style/my_switch"
                        tools:ignore="RtlHardcoded,UseSwitchCompatOrMaterialXml" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_subtitleDataContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_subtitleDataTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_subtitle_data"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/activity_widget_config_subtitleDataSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown"
                        android:layout_toEndOf="@id/activity_widget_config_subtitleDataTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:theme="@style/spinner_item"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_blackTextContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_blackTextTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_text_color"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/activity_widget_config_blackTextSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown"
                        android:layout_toEndOf="@id/activity_widget_config_blackTextTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:theme="@style/spinner_item"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_textSizeContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_textSizeTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_text_size"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <com.google.android.material.slider.Slider
                        android:id="@+id/activity_widget_config_textSizeSlider"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@id/activity_widget_config_textSizeTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_clockFontContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_clockFontTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_clock_font"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <androidx.appcompat.widget.AppCompatSpinner
                        android:id="@+id/activity_widget_config_clockFontSpinner"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown"
                        android:layout_toEndOf="@id/activity_widget_config_clockFontTitle"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:theme="@style/spinner_item"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_hideAlternateCalendarContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_hideAlternateCalendarTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_hide_alternate_calendar"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <Switch
                        android:id="@+id/activity_widget_config_hideAlternateCalendarSwitch"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/normal_margin"
                        style="@style/my_switch"
                        tools:ignore="RtlHardcoded,UseSwitchCompatOrMaterialXml" />

                </RelativeLayout>

                <RelativeLayout
                    android:id="@+id/activity_widget_config_alignEndContainer"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <TextView
                        android:id="@+id/activity_widget_config_alignEndTitle"
                        android:layout_centerVertical="true"
                        android:layout_margin="@dimen/normal_margin"
                        android:text="@string/widget_label_align_end"
                        android:layout_alignParentStart="true"
                        style="@style/content_text" />

                    <Switch
                        android:id="@+id/activity_widget_config_alignEndSwitch"
                        android:layout_alignParentEnd="true"
                        android:layout_centerVertical="true"
                        android:layout_marginEnd="@dimen/normal_margin"
                        style="@style/my_switch"
                        tools:ignore="RtlHardcoded,UseSwitchCompatOrMaterialXml" />

                </RelativeLayout>

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content" >

                    <Button
                        android:id="@+id/activity_widget_config_doneButton"
                        android:layout_margin="@dimen/normal_margin"
                        android:layout_alignParentEnd="true"
                        android:text="@string/action_done"
                        android:textColor="?attr/colorPrimary"
                        style="@style/material_button"
                        tools:ignore="RtlHardcoded" />

                </RelativeLayout>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/activity_widget_config_custom_subtitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="?android:attr/colorBackground"
            app:behavior_hideable="true"
            app:layout_behavior="@string/bottom_sheet_behavior">

            <androidx.core.widget.NestedScrollView
                android:id="@+id/activity_widget_config_custom_scrollView"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clipToPadding="false">

                <LinearLayout
                    android:orientation="vertical"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content">

                    <com.google.android.material.textfield.TextInputLayout
                        android:id="@+id/activity_widget_config_subtitle_inputLayout"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:padding="@dimen/normal_margin">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/activity_widget_config_subtitle_inputter"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="textFilter"
                            android:hint="@string/widget_label_subtitle_data"
                            android:textColorHint="@color/colorTextSubtitle"
                            android:textColorHighlight="@color/colorTextSubtitle"
                            android:textSize="@dimen/content_text_size"
                            android:textColor="@color/colorTextContent"
                            android:maxLines="1"
                            tools:ignore="Autofill" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <TextView
                        android:text="@string/widget_custom_subtitle_explanation"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:layout_marginBottom="@dimen/normal_margin"
                        style="@style/content_text" />

                    <TextView
                        android:id="@+id/activity_widget_config_custom_subtitle_keywords"
                        android:layout_marginStart="@dimen/normal_margin"
                        android:layout_marginEnd="@dimen/normal_margin"
                        android:layout_marginBottom="@dimen/normal_margin"
                        android:textIsSelectable="true"
                        style="@style/content_text" />

                </LinearLayout>

            </androidx.core.widget.NestedScrollView>

        </com.google.android.material.appbar.AppBarLayout>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</LinearLayout>
