<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- theme -->
    <color name="colorSplashScreen">#141c2c</color>

    <!-- widget S. -->

    <color name="colorWidgetM3Root">@android:color/system_accent2_800</color>
    <color name="colorWidgetM3TextPrimary">@android:color/system_accent2_100</color>
    <color name="colorWidgetM3TextSecondary">@android:color/system_accent1_50</color>

    <!-- material 3. -->

    <color name="md_theme_primary">@android:color/system_accent1_200</color>
    <color name="md_theme_onPrimary">@android:color/system_accent1_800</color>
    <color name="md_theme_primaryContainer">@android:color/system_accent1_700</color>
    <color name="md_theme_onPrimaryContainer">@android:color/system_accent1_100</color>
    <color name="md_theme_secondary">@android:color/system_accent2_200</color>
    <color name="md_theme_onSecondary">@android:color/system_accent2_800</color>
    <color name="md_theme_secondaryContainer">@android:color/system_accent2_700</color>
    <color name="md_theme_onSecondaryContainer">@android:color/system_accent2_100</color>
    <color name="md_theme_tertiary">@android:color/system_accent3_200</color>
    <color name="md_theme_onTertiary">@android:color/system_accent3_800</color>
    <color name="md_theme_tertiaryContainer">@android:color/system_accent3_700</color>
    <color name="md_theme_onTertiaryContainer">@android:color/system_accent3_100</color>
    <color name="md_theme_error">#F2B8B5</color>
    <color name="md_theme_errorContainer">#8C1D18</color>
    <color name="md_theme_onError">#601410</color>
    <color name="md_theme_onErrorContainer">#F9DEDC</color>
    <color name="md_theme_background">@android:color/system_neutral1_1000</color>
    <color name="md_theme_onBackground">@android:color/system_neutral1_0</color>
    <color name="md_theme_surface">@android:color/system_neutral1_900</color>
    <color name="md_theme_onSurface">@android:color/system_neutral1_100</color>
    <color name="md_theme_surfaceVariant">@android:color/system_neutral2_700</color>
    <color name="md_theme_onSurfaceVariant">@android:color/system_neutral2_200</color>
    <color name="md_theme_inverseOnSurface">@android:color/system_neutral1_900</color>
    <color name="md_theme_inverseSurface">@android:color/system_neutral1_100</color>
    <color name="md_theme_inversePrimary">@android:color/system_accent1_900</color>
    <color name="md_theme_shadow">#000000</color>
    <color name="md_theme_primaryInverse">@android:color/system_accent1_400</color>

</resources>
