<resources>
    
    <style name="notification_large_title_text" parent="@style/TextAppearance.Compat.Notification.Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/large_title_text_size</item>
    </style>

    <style name="notification_title_text" parent="@style/TextAppearance.Compat.Notification.Title">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/title_text_size</item>
    </style>

    <style name="notification_content_text" parent="@style/TextAppearance.Compat.Notification.Line2">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/content_text_size</item>
    </style>

    <style name="notification_subtitle_text" parent="@style/TextAppearance.Compat.Notification.Time">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:textSize">@dimen/subtitle_text_size</item>
    </style>

    <style name="BreezyWeatherAndroidSWidget" parent="@android:style/Theme.DeviceDefault">
        <item name="android:textColorPrimary">@color/colorWidgetM3TextPrimary</item>
        <item name="android:textColorSecondary">@color/colorWidgetM3TextSecondary</item>
    </style>

</resources>
