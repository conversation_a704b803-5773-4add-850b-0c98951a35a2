<?xml version="1.0" encoding="utf-8"?>
<resources>

    <!-- widget S. -->

    <color name="colorWidgetM3Root">@android:color/system_accent1_50</color>
    <color name="colorWidgetM3TextPrimary">@android:color/system_accent1_700</color>
    <color name="colorWidgetM3TextSecondary">@android:color/system_accent1_800</color>

    <!-- material 3. -->

    <color name="md_theme_primary">@android:color/system_accent1_600</color>
    <color name="md_theme_onPrimary">@android:color/system_accent1_0</color>
    <color name="md_theme_primaryContainer">@android:color/system_accent1_100</color>
    <color name="md_theme_onPrimaryContainer">@android:color/system_accent1_900</color>
    <color name="md_theme_secondary">@android:color/system_accent2_600</color>
    <color name="md_theme_onSecondary">@android:color/system_accent2_0</color>
    <color name="md_theme_secondaryContainer">@android:color/system_accent2_100</color>
    <color name="md_theme_onSecondaryContainer">@android:color/system_accent2_900</color>
    <color name="md_theme_tertiary">@android:color/system_accent3_600</color>
    <color name="md_theme_onTertiary">@android:color/system_accent3_0</color>
    <color name="md_theme_tertiaryContainer">@android:color/system_accent3_100</color>
    <color name="md_theme_onTertiaryContainer">@android:color/system_accent3_900</color>
    <color name="md_theme_error">#B3261E</color>
    <color name="md_theme_errorContainer">#F9DEDC</color>
    <color name="md_theme_onError">#FFFFFF</color>
    <color name="md_theme_onErrorContainer">#410E0B</color>
    <color name="md_theme_background">@android:color/system_neutral1_0</color>
    <color name="md_theme_onBackground">@android:color/system_neutral1_1000</color>
    <color name="md_theme_surface">@android:color/system_neutral1_10</color>
    <color name="md_theme_onSurface">@android:color/system_neutral1_900</color>
    <color name="md_theme_surfaceVariant">@android:color/system_neutral2_100</color>
    <color name="md_theme_onSurfaceVariant">@android:color/system_neutral2_700</color>
    <color name="md_theme_inverseOnSurface">@android:color/system_neutral1_100</color>
    <color name="md_theme_inverseSurface">@android:color/system_neutral1_900</color>
    <color name="md_theme_inversePrimary">@android:color/system_accent1_600</color>
    <color name="md_theme_shadow">#000000</color>
    <color name="md_theme_primaryInverse">@android:color/system_accent1_600</color>

</resources>
