/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, either version 3 of the License, or (at your
 * option) any later version.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of ME<PERSON>HANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with <PERSON>zy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.sources.nws.json

import kotlinx.serialization.Serializable

@Serializable
data class NwsCurrentResult(
    val geometry: NwsPointLocationGeometry? = null,
    val properties: NwsCurrentProperties? = null,
)
