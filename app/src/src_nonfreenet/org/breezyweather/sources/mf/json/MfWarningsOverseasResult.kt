/**
 * This file is part of Breezy Weather.
 *
 * Breezy Weather is free software: you can redistribute it and/or modify it
 * under the terms of the GNU Lesser General Public License as published by the
 * Free Software Foundation, version 3 of the License.
 *
 * Breezy Weather is distributed in the hope that it will be useful, but
 * WITHOUT ANY WARRANTY; without even the implied warranty of MERCHANTABILITY
 * or FITNESS FOR A PARTICULAR PURPOSE. See the GNU Lesser General Public
 * License for more details.
 *
 * You should have received a copy of the GNU Lesser General Public License
 * along with Breezy Weather. If not, see <https://www.gnu.org/licenses/>.
 */

package org.breezyweather.sources.mf.json

import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import org.breezyweather.common.serializer.DateSerializer
import java.util.Date

/**
 * Mf warning overseas (old endpoint)
 */
@Serializable
data class MfWarningsOverseasResult(
    @Serializable(DateSerializer::class) @SerialName("update_time") val updateTime: Date? = null,
    @Serializable(DateSerializer::class) @SerialName("end_validity_time") val endValidityTime: Date? = null,
    @SerialName("color_max") val colorMax: Int? = null,
    val timelaps: List<MfWarningOverseasTimelaps>? = null,
    // @SerialName("phenomenons_items") val phenomenonsItems: List<MfWarningPhenomenonMaxColor>? = null,
    val advices: List<MfWarningOverseasAdvice>? = null,
    val consequences: List<MfWarningOverseasConsequence>? = null,
    // @SerialName("max_count_items") val maxCountItems: List<MfWarningMaxCountItems>? = null,
    val comments: MfWarningOverseasComments? = null,
    val text: MfWarningOverseasComments? = null,
    @SerialName("text_avalanche") val textAvalanche: MfWarningOverseasComments? = null,
)
