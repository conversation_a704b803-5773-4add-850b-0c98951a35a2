name: Issue
description: Report a bug/issue with Breezy Weather
labels: Bug, Needs review
body:
  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to reproduce
      placeholder: |
        Example:
          1. First step
          2. Second step
          3. Issue here
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected behavior
      description: Explain what should happen.
      placeholder: |
        Example:
          "X should happen…"
    validations:
      required: true

  - type: textarea
    id: actual-behavior
    attributes:
      label: Actual behavior
      description: Explain what actually happens.
      placeholder: |
        Example:
          "X happened instead…"
    validations:
      required: true

  - type: input
    id: weather-source
    attributes:
      label: Weather source used
      description: You can find it on bottom of homepage, or in location list.
      placeholder: |
        Example: "AccuWeather"
    validations:
      required: true

  - type: input
    id: app-version
    attributes:
      label: Breezy Weather version
      description: You can find it on Info icon in app settings or from Android settings for the app. We expect a version number (and optionally the flavor) here, “Latest” is not a valid answer.
      placeholder: |
        Examples: "5.2.0 or 5.2.0_freenet"
    validations:
      required: true

  - type: input
    id: android-version
    attributes:
      label: Android version
      description: You can find it in your Android settings. If you use a custom ROM, please write it here.
      placeholder: |
        Example: "Android 13"
    validations:
      required: true

  - type: input
    id: device
    attributes:
      label: Device
      description: Your device and model.
      placeholder: |
        Example: "Samsung Galaxy A54"
    validations:
      required: true

  - type: textarea
    id: other-details
    attributes:
      label: Other details
      placeholder: |
        Additional details and attachments. If you have a refresh issue, please include here if you use current location (with the location service used) or manually added location, and export crash log from Settings > Debug.

  - type: checkboxes
    id: acknowledgements
    attributes:
      label: Acknowledgements
      description: Your issue will be closed if you don’t read this carefully.
      options:
        - label: I have searched the existing issues and this is a new ticket, **NOT** a duplicate or related to another open OR closed issue.
          required: true
        - label: I checked that my issue is not covered in the [Frequently Asked Questions](https://github.com/breezy-weather/breezy-weather/blob/main/HELP.md) document.
          required: true
        - label: I have updated the app to **[version 5.4.8 or later](https://github.com/breezy-weather/breezy-weather/releases)** and I can reproduce the issue in this version.
          required: true
        - label: I wrote my request in English, and any screenshot I attached are also in English (language can be changed from Settings > Appearance), unless my issue only happens with a specific language. I can write in my native language under the English text, if necessary.
          required: true
        - label: I understand that this app is distributed in the hope that it will be useful, but WITHOUT ANY WARRANTY, so I may not get a positive reply to my request, if any at all.
          required: true
